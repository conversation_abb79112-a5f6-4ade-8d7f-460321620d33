<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-secondaryLabel: rgba(60,60,67,0.6);
            --ios-separator: rgba(60,60,67,0.1);
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
            background: #F2F2F7;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            padding: 40px;
            text-align: center;
        }

        .demo-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 24px;
            color: #1c1c1e;
        }

        .demo-subtitle {
            font-size: 18px;
            color: var(--ios-secondaryLabel);
            margin-bottom: 40px;
            line-height: 1.5;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* iPhone 手机壳样式 */
        .iphone-frame {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 44px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1),
                        0 0 0 11px #1c1c1e;
            margin: 40px auto;
            overflow: hidden;
        }

        .iphone-notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 210px;
            height: 30px;
            background: #1c1c1e;
            border-bottom-left-radius: 24px;
            border-bottom-right-radius: 24px;
            z-index: 2;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            overflow: hidden;
            position: relative;
            background: #F2F2F7;
        }

        .iphone-frame iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .demo-hint {
            margin-top: 24px;
            color: var(--ios-secondaryLabel);
            font-size: 14px;
        }

        .demo-arrow {
            font-size: 24px;
            color: var(--ios-blue);
            margin-bottom: 16px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">乐享友邻</h1>
        <p class="demo-subtitle">基于 iOS 设计规范的社区互助平台，让邻里互助更简单。</p>
        
        <div class="demo-arrow">
            <i class="fas fa-arrow-down"></i>
        </div>

        <div class="iphone-frame">
            <div class="iphone-notch"></div>
            <div class="iphone-screen">
                <iframe src="modules/navigation/home.html" frameborder="0"></iframe>
            </div>
        </div>

        <p class="demo-hint">↑ 在手机界面中浏览和交互</p>
    </div>
</body>
</html> 