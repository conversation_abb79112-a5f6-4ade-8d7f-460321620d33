# 乐享友邻 App 项目文档

## 简介

乐享友邻是面向社区居民的综合服务应用，旨在提升居民生活质量和促进邻里互动。核心功能包括二手交易、房屋租售、停车位租赁、邻里拼单等，同时提供用户认证机制确保平台安全可信。

## 系统架构

### 核心模块

1. **用户认证**：处理用户注册、登录和身份认证
2. **小区管理**：管理用户关联的小区信息和切换
3. **二手交易**：发布和浏览二手闲置物品
4. **房屋租赁**：发布和查询房屋租赁信息
5. **停车位租售**：管理小区内的停车位租售服务
6. **邻里拼单**：居民发起的团购服务，支持生鲜、电商商品等共同采购
7. **邻里集市**：邻里间农产品的买卖和交流平台
8. **消息通知**：处理系统通知和用户间消息
9. **个人中心**：管理用户信息和设置

### 技术架构

前端使用 HTML5、TailwindCSS 和 JavaScript，采用响应式设计确保移动设备上的良好体验。后端提供 RESTful API 接口支持各项功能。

## 功能详解

### 邻里拼单功能

最新添加的核心功能，允许社区居民发起和参与团购活动：

- **拼单首页**：展示所有进行中的拼单，支持分类筛选和搜索
- **发布拼单**：用户可创建新拼单，设置参与人数、截止时间和详细信息
- **拼单详情**：查看拼单详情、参与状态和联系方式
- **参与管理**：加入拼单、查看参与者信息和进度

### 用户认证与权限

不同用户身份（游客、已认证业主、租户、物业、农户）拥有不同权限：

- **游客**：仅浏览基本信息
- **已认证业主**：完整发布权限和专属标识
- **已认证租户**：租户专属标识和相关权限
- **已认证物业**：发布小区公告和管理信息
- **已认证农户**：发布农产品和管理店铺

### 小区与内容关联

所有内容（二手物品、房源、停车位、拼单、农产品）均与特定小区关联：

- 用户可切换小区，筛选相关内容
- 内容可设置仅对特定小区可见
- 距离计算基于当前小区位置

## 界面设计

### 导航结构

应用采用底部标签栏导航，包含五个主要入口：

1. **首页**：展示主要功能和热门内容
2. **发现**：探索更多社区内容和服务
3. **发布**：发布各类信息的中心按钮
4. **消息**：查看通知和对话
5. **我的**：访问个人中心和设置

### 视觉风格

- **主色调**：蓝色系 (#007AFF)，代表社区连接
- **辅助色**：
  - 黄色：业主标识
  - 蓝色：租户标识
  - 紫色：物业标识
  - 绿色：农户和集市标识
- **设计风格**：iOS风格界面，注重易用性和视觉层次

## 实现与优化

### 技术栈

- **前端**：HTML5/CSS3、TailwindCSS、JavaScript
- **图标系统**：FontAwesome 和 SVG 图标
- **动效**：CSS 过渡和交互动画

### 性能优化策略

- **图片懒加载**：提升页面加载速度
- **响应式设计**：适配不同尺寸的移动设备
- **CSS优化**：使用 Tailwind 减少冗余代码
- **渐进式加载**：先加载骨架，再填充内容

## 项目结构

```
hoodly-joy/
├── modules/               # 模块化功能目录
│   ├── navigation/        # 导航相关页面
│   ├── auth/              # 用户认证模块
│   ├── community/         # 小区管理模块
│   ├── secondhand/        # 二手交易模块
│   ├── housing/           # 房屋租赁模块
│   ├── parking/           # 停车位租售模块
│   ├── group-buy/         # 邻里拼单模块
│   ├── market/            # 邻里集市模块
│   ├── message/           # 消息通知模块
│   ├── profile/           # 个人中心模块
│   └── common/            # 共享组件和资源
├── images/                # 公共图像资源
├── styles.css             # 全局样式
└── api.md                 # API接口文档
```

## 迭代计划

1. **阶段一**：用户系统和小区管理（已完成）
2. **阶段二**：二手交易和消息功能（已完成）
3. **阶段三**：房屋和停车位租售（已完成）
4. **阶段四**：邻里拼单和集市功能（当前阶段）
5. **阶段五**：用户体验优化和性能提升（计划中）

## 总结

乐享友邻致力于打造智慧社区生态系统，通过完善的用户认证机制和多小区管理功能，为居民提供个性化、本地化的服务体验。新增的邻里拼单功能进一步促进了社区居民间的互助合作，满足现代社区居民的多样化需求。