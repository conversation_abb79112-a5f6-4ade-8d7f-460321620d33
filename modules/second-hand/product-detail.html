<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 闲置商品详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-bar h1 {
            font-weight: 600;
            font-size: 17px;
            color: var(--ios-label);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-right {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-medium);
            margin-bottom: 10px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04), 0 1px 2px rgba(0,0,0,0.02);
        }
        
        /* iOS图片轮播计数器 */
        .ios-gallery-counter {
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            position: absolute;
            bottom: 12px;
            right: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* iOS轮播指示器 */
        .ios-gallery-indicators {
            display: flex;
            justify-content: center;
            position: absolute;
            bottom: 16px;
            left: 0;
            right: 0;
        }
        
        .ios-gallery-dot {
            width: 6px;
            height: 6px;
            border-radius: 3px;
            background-color: rgba(255,255,255,0.4);
            margin: 0 2px;
        }
        
        .ios-gallery-dot.active {
            background-color: white;
            width: 12px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            display: inline-flex;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            background-color: rgba(0, 122, 255, 0.08);
            color: var(--ios-blue);
            margin-right: 6px;
        }
        
        /* iOS底部操作栏 */
        .ios-action-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            display: flex;
            align-items: center;
            padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
            box-shadow: 0 -0.5px 0 var(--ios-separator);
            z-index: 100;
        }
        
        /* iOS主操作按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 12px 20px;
            flex: 1;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-primary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.8);
        }
        
        /* iOS副操作按钮 */
        .ios-secondary-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: var(--ios-secondaryLabel);
            padding: 0 16px;
        }
        
        .ios-secondary-button:active {
            opacity: 0.6;
        }
        
        .ios-secondary-button i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .ios-secondary-button span {
            font-size: 12px;
            font-weight: 500;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
        
        /* iOS条目分隔线 */
        .ios-divider {
            height: 0.5px;
            background-color: var(--ios-separator);
            margin: 12px 0;
        }
        
        /* iOS列表项样式 */
        .ios-list-item {
            display: flex;
            padding: 14px 0;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-list-item:last-child {
            border-bottom: none;
        }
        
        /* iOS圆形图标 */
        .ios-circle-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background-color: rgba(0, 122, 255, 0.08);
            color: var(--ios-blue);
            margin-right: 12px;
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">闲置商品详情</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1>商品详情</h1>
                <button class="ios-button ios-haptic ios-nav-right">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>

            <!-- 页面内容区 -->
            <div class="pb-32">
                <!-- 商品图片轮播 -->
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1678911820864-e2c567c655d7" class="w-full aspect-square object-cover">
                    <div class="ios-gallery-counter">1/4</div>
                    <div class="ios-gallery-indicators">
                        <div class="ios-gallery-dot active"></div>
                        <div class="ios-gallery-dot"></div>
                        <div class="ios-gallery-dot"></div>
                        <div class="ios-gallery-dot"></div>
                    </div>
                </div>

                <!-- 商品基本信息 -->
                <div class="ios-card m-3 p-4 ios-fade-in ios-fade-in-delay-1">
                    <div class="flex justify-between items-start">
                        <div>
                            <h2 class="text-xl font-semibold">iPhone 14 Pro Max</h2>
                            <p class="text-[#FF3B30] text-xl font-semibold mt-2">¥7999</p>
                        </div>
                        <button class="ios-secondary-button ios-haptic" id="favoriteBtn">
                            <i class="far fa-heart"></i>
                            <span>收藏</span>
                        </button>
                    </div>
                    <div class="flex items-center mt-3 flex-wrap">
                        <span class="ios-tag">95新</span>
                        <span class="ios-tag">整机保修10个月</span>
                        <span class="text-sm text-[#3C3C434D] ml-auto">浏览 238</span>
                    </div>
                </div>

                <!-- 卖家信息 -->
                <div class="ios-card mx-3 p-4 ios-fade-in ios-fade-in-delay-2">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="w-12 h-12 rounded-full object-cover border border-gray-100">
                        <div class="ml-3 flex-1">
                            <div class="flex items-center">
                                <h3 class="font-medium">张三</h3>
                                <span class="ml-2 ios-tag bg-[#34C759] bg-opacity-10 text-[#34C759]">已实名</span>
                            </div>
                            <p class="text-sm text-[#3C3C43B2] mt-1">阳光花园小区</p>
                        </div>
                        <button class="bg-[#007AFF] text-white px-4 py-2 rounded-full text-sm ios-button ios-haptic">联系卖家</button>
                    </div>
                </div>

                <!-- 闲置商品详情 -->
                <div class="ios-card mx-3 p-4 ios-fade-in ios-fade-in-delay-3">
                    <h3 class="text-lg font-semibold mb-3">商品详情</h3>
                    <div class="space-y-1">
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-calendar-alt text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">购买日期</div>
                                <div class="font-medium">2023年3月</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-clock text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">使用时长</div>
                                <div class="font-medium">9个月</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-star text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">成色</div>
                                <div class="font-medium">95新，无划痕，无维修</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-box text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">随机配件</div>
                                <div class="font-medium">原装充电器、数据线、耳机（未拆封）</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-question-circle text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">转手原因</div>
                                <div class="font-medium">换新机</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-check-circle text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">验机方式</div>
                                <div class="font-medium">支持当面交易，可验机</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-hand-holding-usd text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">交易方式</div>
                                <div class="font-medium">线下自提，支持微信/支付宝支付</div>
                            </div>
                        </div>
                        
                        <div class="ios-list-item py-4">
                            <div class="ios-circle-icon">
                                <i class="fas fa-map-marker-alt text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm text-[#3C3C43B2]">交易地点</div>
                                <div class="font-medium">阳光花园小区附近</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 相似推荐 -->
                <div class="ios-card mx-3 p-4 mt-3 ios-fade-in ios-fade-in-delay-4">
                    <h3 class="text-lg font-semibold mb-3">相似推荐</h3>
                    <div class="flex overflow-x-auto -mx-4 px-4 pb-2 space-x-3 scrollbar-none">
                        <div class="w-32 flex-shrink-0 ios-haptic">
                            <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" class="w-full aspect-square object-cover rounded-lg shadow-sm">
                            <p class="text-sm mt-2 font-medium truncate">iPhone 13 Pro</p>
                            <p class="text-xs text-[#FF3B30] font-semibold">¥5299</p>
                        </div>
                        <div class="w-32 flex-shrink-0 ios-haptic">
                            <img src="https://images.unsplash.com/photo-1592899677977-9c10ca588bbd" class="w-full aspect-square object-cover rounded-lg shadow-sm">
                            <p class="text-sm mt-2 font-medium truncate">iPhone 12 Pro Max</p>
                            <p class="text-xs text-[#FF3B30] font-semibold">¥4599</p>
                        </div>
                        <div class="w-32 flex-shrink-0 ios-haptic">
                            <img src="https://images.unsplash.com/photo-1580910051074-3eb694886505" class="w-full aspect-square object-cover rounded-lg shadow-sm">
                            <p class="text-sm mt-2 font-medium truncate">iPhone 14 Pro</p>
                            <p class="text-xs text-[#FF3B30] font-semibold">¥6899</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="ios-action-bar">
                <button class="ios-secondary-button ios-haptic mr-2">
                    <i class="fas fa-share-alt"></i>
                    <span>分享</span>
                </button>
                <button class="ios-primary-button ios-haptic">联系卖家</button>
            </div>
        </div>
    </div>
    
    <style>
        /* 隐藏滚动条但保持功能 */
        .scrollbar-none {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .scrollbar-none::-webkit-scrollbar {
            display: none;
        }
        
        /* 添加轮播图动画效果 */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* 点赞心形颜色变化动画 */
        @keyframes heartPulse {
            0% {
                transform: scale(1);
            }
            15% {
                transform: scale(1.3);
            }
            30% {
                transform: scale(0.95);
            }
            45% {
                transform: scale(1.1);
            }
            60% {
                transform: scale(1);
            }
        }
        
        .heart-pulse {
            animation: heartPulse 0.8s ease-in-out;
        }
    </style>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 收藏按钮交互
            const favoriteBtn = document.getElementById('favoriteBtn');
            if (favoriteBtn) {
                favoriteBtn.addEventListener('click', function() {
                    const icon = favoriteBtn.querySelector('i');
                    const isActive = icon.classList.contains('fas');
                    
                    if (!isActive) {
                        // 变为收藏状态
                        icon.classList.remove('far');
                        icon.classList.add('fas', 'heart-pulse');
                        icon.style.color = '#FF2D55';
                        
                        // 较强的触感反馈
                        if ('vibrate' in navigator) {
                            navigator.vibrate([10, 30, 10]);
                        }
                    } else {
                        // 取消收藏状态
                        icon.classList.remove('fas', 'heart-pulse');
                        icon.classList.add('far');
                        icon.style.color = '';
                    }
                });
            }
            
            // 图片轮播点击交互
            const galleryImg = document.querySelector('.relative img');
            const indicators = document.querySelectorAll('.ios-gallery-dot');
            const counter = document.querySelector('.ios-gallery-counter');
            
            if (galleryImg && indicators.length > 0) {
                galleryImg.addEventListener('click', function() {
                    // 模拟图片切换
                    let currentActive = document.querySelector('.ios-gallery-dot.active');
                    let currentIndex = Array.from(indicators).indexOf(currentActive);
                    let nextIndex = (currentIndex + 1) % indicators.length;
                    
                    // 更新指示器状态
                    currentActive.classList.remove('active');
                    indicators[nextIndex].classList.add('active');
                    
                    // 更新计数器
                    counter.textContent = `${nextIndex + 1}/${indicators.length}`;
                    
                    // 添加切换动画
                    galleryImg.style.animation = 'slideIn 0.3s ease-out forwards';
                    setTimeout(() => {
                        galleryImg.style.animation = '';
                    }, 300);
                });
            }
            
            // 确保所有淡入元素最终显示
            setTimeout(() => {
                document.querySelectorAll('.ios-fade-in').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        });
    </script>
</body>
</html> 