<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 发闲置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-bar h1 {
            font-weight: 600;
            font-size: 17px;
            color: var(--ios-label);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-right {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-medium);
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
        }
        
        /* iOS表单组 */
        .ios-form-group {
            margin-bottom: 20px;
        }
        
        .ios-form-group:last-child {
            margin-bottom: 0;
        }
        
        /* iOS表单标签 */
        .ios-form-label {
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-label);
            margin-bottom: 8px;
            display: block;
        }
        
        /* iOS输入框 */
        .ios-input {
            width: 100%;
            padding: 12px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid rgba(60,60,67,0.15);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            transition: all 0.2s ease;
            outline: none;
            -webkit-appearance: none;
            color: var(--ios-label);
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
        }
        
        .ios-input::placeholder {
            color: var(--ios-tertiaryLabel);
        }
        
        /* iOS选择框 */
        .ios-select {
            width: 100%;
            padding: 12px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid rgba(60,60,67,0.15);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            transition: all 0.2s ease;
            outline: none;
            -webkit-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 16px center;
            padding-right: 40px;
            color: var(--ios-label);
        }
        
        .ios-select:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
        }
        
        /* iOS图片上传区域 */
        .ios-upload-area {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 12px;
        }
        
        .ios-upload-item {
            aspect-ratio: 1;
            border-radius: 10px;
            border: 1.5px dashed rgba(60,60,67,0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: rgba(118, 118, 128, 0.06);
            transition: all 0.15s ease;
            position: relative;
            overflow: hidden;
        }
        
        .ios-upload-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.03);
            opacity: 0;
            transition: opacity 0.15s ease;
        }
        
        .ios-upload-item:active::before {
            opacity: 1;
        }
        
        .ios-upload-item:active {
            transform: scale(0.95);
            background-color: rgba(60,60,67,0.04);
        }
        
        .ios-upload-main {
            border-color: var(--ios-blue);
            border-style: dashed;
            border-width: 1.5px;
            background-color: rgba(0,122,255,0.04);
        }
        
        .ios-upload-icon {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 14px;
            background-color: var(--ios-blue);
            color: white;
            margin-bottom: 6px;
            box-shadow: 0 2px 6px rgba(0,122,255,0.2);
        }
        
        /* iOS底部操作栏 */
        .ios-action-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
            box-shadow: 0 -0.5px 0 var(--ios-separator);
            z-index: 100;
            gap: 12px;
        }
        
        /* iOS次要按钮 */
        .ios-secondary-button {
            background-color: rgba(118, 118, 128, 0.08);
            color: var(--ios-blue);
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 12px 20px;
            flex: 1;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-secondary-button:active {
            transform: scale(0.97);
            background-color: rgba(118, 118, 128, 0.14);
        }
        
        /* iOS主要按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 12px 20px;
            flex: 1;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-primary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.8);
        }
        
        /* iOS文本区域 */
        .ios-textarea {
            width: 100%;
            padding: 12px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid rgba(60,60,67,0.15);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            transition: all 0.2s ease;
            outline: none;
            -webkit-appearance: none;
            resize: none;
            min-height: 120px;
            color: var(--ios-label);
        }
        
        .ios-textarea:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
        }
        
        /* iOS分组卡片标题 */
        .ios-card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--ios-label);
        }
        
        /* iOS小提示 */
        .ios-hint {
            font-size: 13px;
            color: var(--ios-tertiaryLabel);
            margin-top: 6px;
        }
        
        /* iOS选项卡 */
        .ios-option-tab {
            display: flex;
            border-radius: 8px;
            overflow: hidden;
            background-color: rgba(118, 118, 128, 0.08);
            padding: 2px;
        }
        
        .ios-option-tab-item {
            flex: 1;
            padding: 8px 10px;
            text-align: center;
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-secondaryLabel);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            z-index: 1;
        }
        
        .ios-option-tab-item.active {
            color: var(--ios-label);
        }
        
        .ios-option-tab-indicator {
            position: absolute;
            top: 2px;
            left: 2px;
            bottom: 2px;
            border-radius: 6px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            z-index: 0;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">发闲置</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1>发布闲置</h1>
                <button class="ios-button ios-haptic ios-nav-right">帮助</button>
            </div>

            <!-- 表单内容区 -->
            <div class="p-4 space-y-5 pb-40">
                <!-- 图片上传 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-1">
                    <h3 class="ios-card-title">商品图片</h3>
                    <div class="ios-upload-area">
                        <div class="ios-upload-item ios-upload-main ios-haptic">
                            <div class="ios-upload-icon">
                                <i class="fas fa-camera text-xs"></i>
                            </div>
                            <span class="text-xs font-medium text-[#007AFF]">主图</span>
                        </div>
                        <div class="ios-upload-item ios-haptic">
                            <i class="fas fa-plus text-[#8E8E93]"></i>
                        </div>
                        <div class="ios-upload-item ios-haptic">
                            <i class="fas fa-plus text-[#8E8E93]"></i>
                        </div>
                        <div class="ios-upload-item ios-haptic">
                            <i class="fas fa-plus text-[#8E8E93]"></i>
                        </div>
                    </div>
                    <p class="ios-hint mt-2">最多上传9张图片，主图将展示在列表中</p>
                </div>

                <!-- 基本信息 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-2">
                    <h3 class="ios-card-title">基本信息</h3>
                    
                    <div class="ios-form-group">
                        <label class="ios-form-label">标题</label>
                        <input type="text" placeholder="请输入商品标题" class="ios-input">
                    </div>

                    <div class="ios-form-group">
                        <label class="ios-form-label">分类</label>
                        <select class="ios-select">
                            <option>服装</option>
                            <option>电子产品</option>
                            <option>家居用品</option>
                            <option>图书</option>
                            <option>运动户外</option>
                            <option>美妆</option>
                            <option>母婴用品</option>
                            <option>其他</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="ios-form-group">
                            <label class="ios-form-label">价格</label>
                            <div class="relative">
                                <input type="text" inputmode="decimal" placeholder="¥" class="ios-input pl-8">
                                <span class="absolute left-4 top-[50%] transform -translate-y-[50%] text-[#3C3C43B2] font-medium">¥</span>
                            </div>
                        </div>
                        <div class="ios-form-group">
                            <label class="ios-form-label">原价</label>
                            <div class="relative">
                                <input type="text" inputmode="decimal" placeholder="¥" class="ios-input pl-8">
                                <span class="absolute left-4 top-[50%] transform -translate-y-[50%] text-[#3C3C43B2] font-medium">¥</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-form-label">成色</label>
                        <select class="ios-select">
                            <option>全新</option>
                            <option>95新以上</option>
                            <option>9成新</option>
                            <option>8成新</option>
                            <option>7成新</option>
                            <option>6成新及以下</option>
                        </select>
                    </div>
                </div>

                <!-- 商品描述 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-3">
                    <h3 class="ios-card-title">商品描述</h3>
                    <textarea placeholder="请详细描述商品的成色、使用感受、购买渠道、转手原因等信息，让买家更好地了解商品情况。" class="ios-textarea"></textarea>
                </div>
                
                <!-- 交易信息 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-4">
                    <h3 class="ios-card-title">交易信息</h3>
                    
                    <div class="ios-form-group">
                        <label class="ios-form-label">交易方式</label>
                        <div class="ios-option-tab relative" id="tradeMethodTab">
                            <button class="ios-option-tab-item active ios-haptic">当面交易</button>
                            <button class="ios-option-tab-item ios-haptic">快递邮寄</button>
                            <div class="ios-option-tab-indicator" style="width: calc(50% - 4px);"></div>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-form-label">交易地点</label>
                        <select class="ios-select">
                            <option>阳光花园小区</option>
                            <option>添加其他地点</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="ios-action-bar">
                <button class="ios-secondary-button ios-haptic">保存草稿</button>
                <button class="ios-primary-button ios-haptic">立即发布</button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 交易方式选项卡交互
            const tradeMethodTab = document.getElementById('tradeMethodTab');
            if (tradeMethodTab) {
                const tabItems = tradeMethodTab.querySelectorAll('.ios-option-tab-item');
                const indicator = tradeMethodTab.querySelector('.ios-option-tab-indicator');
                
                tabItems.forEach((item, index) => {
                    item.addEventListener('click', function() {
                        // 移除所有激活状态
                        tabItems.forEach(i => i.classList.remove('active'));
                        // 添加当前激活状态
                        this.classList.add('active');
                        
                        // 移动指示器
                        const itemWidth = item.offsetWidth;
                        const itemPosition = index * itemWidth;
                        indicator.style.width = `${itemWidth - 4}px`;
                        indicator.style.transform = `translateX(${itemPosition}px)`;
                        
                        // 添加强烈的触感反馈
                        if ('vibrate' in navigator) {
                            navigator.vibrate([5, 10, 5]);
                        }
                    });
                });
            }
            
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.ios-input, .ios-textarea, .ios-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    // 轻微触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate(5);
                    }
                    
                    // 添加轻微动画效果
                    this.style.transition = 'transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    this.style.transform = 'scale(1.005)';
                });
                
                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });
            
            // 图片上传交互
            const uploadItems = document.querySelectorAll('.ios-upload-item');
            uploadItems.forEach((item, index) => {
                item.addEventListener('click', function() {
                    // 添加具有差异的触感反馈
                    if ('vibrate' in navigator) {
                        if (index === 0) {
                            // 主图有特殊触感
                            navigator.vibrate([10, 20, 10]);
                        } else {
                            navigator.vibrate(15);
                        }
                    }
                    
                    // 模拟图片上传的涟漪效果
                    const ripple = document.createElement('div');
                    ripple.classList.add('upload-ripple');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.background = 'rgba(0,122,255,0.1)';
                    ripple.style.width = '100%';
                    ripple.style.height = '100%';
                    ripple.style.left = '0';
                    ripple.style.top = '0';
                    ripple.style.pointerEvents = 'none';
                    
                    this.appendChild(ripple);
                    
                    // 触发重绘
                    ripple.offsetWidth;
                    
                    ripple.style.transform = 'scale(1)';
                    ripple.style.opacity = '0';
                    ripple.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    
                    setTimeout(() => {
                        if (ripple.parentNode === this) {
                            this.removeChild(ripple);
                        }
                    }, 600);
                });
            });
            
            // 确保所有淡入元素最终显示
            setTimeout(() => {
                document.querySelectorAll('.ios-fade-in').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        });
    </script>
</body>
</html> 
</html> 