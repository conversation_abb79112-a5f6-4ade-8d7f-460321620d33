openapi: 3.0.3
info:
  title: 乐享友邻 - 二手闲置市场API
  description: 二手闲置交易平台后端接口文档，包含商品管理、搜索、发布、收藏等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  /products:
    post:
      tags:
        - 商品管理
      summary: 获取商品列表
      description: 根据筛选条件获取二手商品列表，支持分页、搜索、分类筛选等功能
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductListRequest'
      responses:
        '200':
          description: 成功获取商品列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /products/{productId}:
    get:
      tags:
        - 商品管理
      summary: 获取商品详情
      description: 根据商品ID获取二手商品的详细信息
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 成功获取商品详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductDetailResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /products/publish:
    post:
      tags:
        - 商品管理
      summary: 发布商品
      description: 用户发布新的二手商品信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishProductRequest'
      responses:
        '201':
          description: 商品发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishProductResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /products/search:
    post:
      tags:
        - 商品搜索
      summary: 搜索商品
      description: 根据关键词搜索二手商品
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /products/{productId}/favorite:
    post:
      tags:
        - 商品收藏
      summary: 收藏商品
      description: 用户收藏感兴趣的二手商品
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - 商品收藏
      summary: 取消收藏商品
      description: 用户取消收藏商品
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /products/{productId}/contact:
    post:
      tags:
        - 商品交易
      summary: 联系卖家
      description: 买家联系卖家进行交易沟通
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactSellerRequest'
      responses:
        '201':
          description: 联系成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactSellerResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /products/{productId}/similar:
    get:
      tags:
        - 商品推荐
      summary: 获取相似商品推荐
      description: 根据当前商品推荐相似的二手商品
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
        - name: limit
          in: query
          description: 推荐商品数量限制
          schema:
            type: integer
            minimum: 1
            maximum: 20
            default: 6
      responses:
        '200':
          description: 成功获取相似商品推荐
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimilarProductsResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /upload/product-images:
    post:
      tags:
        - 文件上传
      summary: 上传商品图片
      description: 上传二手商品相关图片，支持多张图片上传
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 图片文件数组，最多9张
                isMainImage:
                  type: boolean
                  description: 是否为主图
                  default: false
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: 上传失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ProductListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            category:
              type: string
              description: 商品分类
              example: "手机数码"
              enum: ["全部", "手机数码", "电器", "服装", "美妆", "运动户外", "家居日用", "图书"]
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低价格
                  example: 100
                max:
                  type: number
                  description: 最高价格
                  example: 5000
            condition:
              type: string
              description: 商品成色
              example: "95新以上"
              enum: ["全新", "95新以上", "9成新", "8成新", "7成新", "6成新及以下"]
            location:
              type: string
              description: 交易地点
              example: "阳光花园小区"
        sortBy:
          type: string
          enum: [comprehensive, sales, price_asc, price_desc, time_desc]
          default: comprehensive
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          category: "手机数码"
          priceRange:
            min: 1000
            max: 8000
          condition: "95新以上"
        sortBy: "comprehensive"

    ProductListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 89
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            totalPages:
              type: integer
              description: 总页数
              example: 5
            products:
              type: array
              items:
                $ref: '#/components/schemas/ProductItem'

    ProductItem:
      type: object
      properties:
        id:
          type: string
          description: 商品ID
          example: "product_123456"
        title:
          type: string
          description: 商品标题
          example: "iPhone 14 Pro Max"
        price:
          type: number
          description: 售价
          example: 7999
        originalPrice:
          type: number
          description: 原价
          example: 9999
        condition:
          type: string
          description: 商品成色
          example: "95新"
        category:
          type: string
          description: 商品分类
          example: "手机数码"
        mainImage:
          type: string
          description: 主图URL
          example: "https://images.example.com/product1.jpg"
        imageCount:
          type: integer
          description: 图片总数
          example: 4
        description:
          type: string
          description: 商品简述
          example: "整机保修10个月"
        location:
          type: string
          description: 交易地点
          example: "阳光花园小区"
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        viewCount:
          type: integer
          description: 浏览次数
          example: 238
        favoriteCount:
          type: integer
          description: 收藏次数
          example: 2
        seller:
          type: object
          properties:
            id:
              type: string
              description: 卖家ID
              example: "user_789"
            name:
              type: string
              description: 卖家姓名
              example: "张三"
            avatar:
              type: string
              description: 头像URL
              example: "https://images.example.com/avatar1.jpg"
            isVerified:
              type: boolean
              description: 是否已实名认证
              example: true
        isFavorite:
          type: boolean
          description: 是否已收藏
          example: false

    ProductDetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          $ref: '#/components/schemas/ProductDetail'

    ProductDetail:
      type: object
      properties:
        id:
          type: string
          description: 商品ID
          example: "product_123456"
        title:
          type: string
          description: 商品标题
          example: "iPhone 14 Pro Max"
        price:
          type: number
          description: 售价
          example: 7999
        originalPrice:
          type: number
          description: 原价
          example: 9999
        condition:
          type: string
          description: 商品成色
          example: "95新"
        category:
          type: string
          description: 商品分类
          example: "手机数码"
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 图片URL
                example: "https://images.example.com/product1_1.jpg"
              isMain:
                type: boolean
                description: 是否为主图
                example: true
        description:
          type: string
          description: 商品详细描述
          example: "iPhone 14 Pro Max，95新，无划痕，无维修..."
        details:
          type: object
          properties:
            purchaseDate:
              type: string
              description: 购买日期
              example: "2023年3月"
            usageDuration:
              type: string
              description: 使用时长
              example: "9个月"
            accessories:
              type: string
              description: 随机配件
              example: "原装充电器、数据线、耳机（未拆封）"
            sellReason:
              type: string
              description: 转手原因
              example: "换新机"
            inspectionMethod:
              type: string
              description: 验机方式
              example: "支持当面交易，可验机"
            tradeMethod:
              type: string
              description: 交易方式
              example: "线下自提，支持微信/支付宝支付"
            tradeLocation:
              type: string
              description: 交易地点
              example: "阳光花园小区附近"
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        viewCount:
          type: integer
          description: 浏览次数
          example: 238
        favoriteCount:
          type: integer
          description: 收藏次数
          example: 2
        seller:
          type: object
          properties:
            id:
              type: string
              description: 卖家ID
              example: "user_789"
            name:
              type: string
              description: 卖家姓名
              example: "张三"
            avatar:
              type: string
              description: 头像URL
              example: "https://images.example.com/avatar1.jpg"
            isVerified:
              type: boolean
              description: 是否已实名认证
              example: true
            location:
              type: string
              description: 所在位置
              example: "阳光花园小区"
            phone:
              type: string
              description: 联系电话（脱敏）
              example: "138****6789"
        isFavorite:
          type: boolean
          description: 是否已收藏
          example: false

    PublishProductRequest:
      type: object
      required:
        - title
        - price
        - category
        - condition
        - images
      properties:
        title:
          type: string
          description: 商品标题
          example: "iPhone 14 Pro Max"
          maxLength: 100
        category:
          type: string
          description: 商品分类
          example: "手机数码"
          enum: ["服装", "电子产品", "家居用品", "图书", "运动户外", "美妆", "母婴用品", "其他"]
        price:
          type: number
          description: 售价
          example: 7999
          minimum: 0
        originalPrice:
          type: number
          description: 原价
          example: 9999
          minimum: 0
        condition:
          type: string
          description: 商品成色
          example: "95新以上"
          enum: ["全新", "95新以上", "9成新", "8成新", "7成新", "6成新及以下"]
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 图片URL
                example: "https://images.example.com/product1_1.jpg"
              isMain:
                type: boolean
                description: 是否为主图
                example: true
          maxItems: 9
          minItems: 1
        description:
          type: string
          description: 商品详细描述
          example: "iPhone 14 Pro Max，95新，无划痕，无维修，整机保修10个月..."
          maxLength: 1000
        details:
          type: object
          properties:
            purchaseDate:
              type: string
              description: 购买日期
              example: "2023年3月"
            usageDuration:
              type: string
              description: 使用时长
              example: "9个月"
            accessories:
              type: string
              description: 随机配件
              example: "原装充电器、数据线、耳机（未拆封）"
            sellReason:
              type: string
              description: 转手原因
              example: "换新机"
            inspectionMethod:
              type: string
              description: 验机方式
              example: "支持当面交易，可验机"
        tradeInfo:
          type: object
          required:
            - method
            - location
          properties:
            method:
              type: string
              description: 交易方式
              example: "当面交易"
              enum: ["当面交易", "快递邮寄"]
            location:
              type: string
              description: 交易地点
              example: "阳光花园小区"
      example:
        title: "iPhone 14 Pro Max"
        category: "手机数码"
        price: 7999
        originalPrice: 9999
        condition: "95新以上"
        images:
          - url: "https://images.example.com/product1_1.jpg"
            isMain: true
          - url: "https://images.example.com/product1_2.jpg"
            isMain: false
        description: "iPhone 14 Pro Max，95新，无划痕，无维修，整机保修10个月..."
        details:
          purchaseDate: "2023年3月"
          usageDuration: "9个月"
          accessories: "原装充电器、数据线、耳机（未拆封）"
          sellReason: "换新机"
          inspectionMethod: "支持当面交易，可验机"
        tradeInfo:
          method: "当面交易"
          location: "阳光花园小区"

    PublishProductResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "商品发布成功"
          description: 响应消息
        data:
          type: object
          properties:
            productId:
              type: string
              description: 新创建的商品ID
              example: "product_789012"
            status:
              type: string
              description: 商品状态
              example: "published"
              enum: ["pending_review", "published", "rejected", "sold"]
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"

    ProductSearchRequest:
      type: object
      properties:
        keyword:
          type: string
          description: 搜索关键词
          example: "iPhone"
          maxLength: 100
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            category:
              type: string
              description: 商品分类
              example: "手机数码"
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低价格
                  example: 1000
                max:
                  type: number
                  description: 最高价格
                  example: 8000
      example:
        keyword: "iPhone"
        page: 1
        pageSize: 20
        filters:
          category: "手机数码"
          priceRange:
            min: 1000
            max: 8000

    ContactSellerRequest:
      type: object
      required:
        - message
        - contactPhone
      properties:
        message:
          type: string
          description: 联系消息
          example: "您好，我对这个商品很感兴趣，请问还在吗？"
          maxLength: 200
        contactPhone:
          type: string
          description: 联系电话
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        preferredContactTime:
          type: string
          description: 偏好联系时间
          example: "工作日晚上"
      example:
        message: "您好，我对这个商品很感兴趣，请问还在吗？"
        contactPhone: "13800138000"
        preferredContactTime: "工作日晚上"

    ContactSellerResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "联系成功"
          description: 响应消息
        data:
          type: object
          properties:
            contactId:
              type: string
              description: 联系记录ID
              example: "contact_456789"
            sellerContact:
              type: object
              properties:
                name:
                  type: string
                  description: 卖家姓名
                  example: "张三"
                phone:
                  type: string
                  description: 卖家电话（脱敏）
                  example: "138****6789"
                wechat:
                  type: string
                  description: 微信号（如有）
                  example: "zhang_wechat"
            chatRoomId:
              type: string
              description: 聊天室ID（用于即时通讯）
              example: "chat_room_123"

    SimilarProductsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            products:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 商品ID
                    example: "product_234567"
                  title:
                    type: string
                    description: 商品标题
                    example: "iPhone 13 Pro"
                  price:
                    type: number
                    description: 售价
                    example: 5299
                  mainImage:
                    type: string
                    description: 主图URL
                    example: "https://images.example.com/product2.jpg"
                  condition:
                    type: string
                    description: 商品成色
                    example: "9成新"

    UploadResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "上传成功"
          description: 响应消息
        data:
          type: object
          properties:
            images:
              type: array
              items:
                type: object
                properties:
                  url:
                    type: string
                    description: 图片URL
                    example: "https://images.example.com/product1_1.jpg"
                  filename:
                    type: string
                    description: 文件名
                    example: "product1_1.jpg"
                  size:
                    type: integer
                    description: 文件大小（字节）
                    example: 1024000
                  isMain:
                    type: boolean
                    description: 是否为主图
                    example: true

    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "price字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"

tags:
  - name: 商品管理
    description: 二手商品的基本管理功能，包括列表查询、详情获取、发布等
  - name: 商品搜索
    description: 二手商品搜索相关功能
  - name: 商品收藏
    description: 用户收藏商品相关功能
  - name: 商品交易
    description: 买卖双方交易沟通相关功能
  - name: 商品推荐
    description: 商品推荐算法相关功能
  - name: 文件上传
    description: 图片等文件上传功能
