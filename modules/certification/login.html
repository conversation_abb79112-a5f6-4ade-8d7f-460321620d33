<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-systemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        /* iOS输入框 */
        .ios-input {
            width: 100%;
            padding: 14px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid rgba(60,60,67,0.15);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            transition: all 0.2s ease;
            outline: none;
            -webkit-appearance: none;
            color: var(--ios-label);
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
        }
        
        .ios-input::placeholder {
            color: var(--ios-tertiaryLabel);
        }
        
        /* iOS表单标签 */
        .ios-form-label {
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-label);
            margin-bottom: 8px;
            display: block;
        }
        
        /* iOS主按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 14px 20px;
            width: 100%;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-primary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.8);
        }
        
        /* iOS社交按钮 */
        .ios-social-button {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--ios-secondarySystemBackground);
            transition: all 0.15s ease;
        }
        
        .ios-social-button:active {
            transform: scale(0.92);
            background-color: rgba(118, 118, 128, 0.2);
        }
        
        /* iOS链接按钮 */
        .ios-link-button {
            color: var(--ios-blue);
            font-size: 15px;
            font-weight: 500;
            transition: opacity 0.2s ease;
        }
        
        .ios-link-button:active {
            opacity: 0.6;
        }
        
        /* iOS复选框 */
        .ios-checkbox {
            appearance: none;
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 1px solid rgba(60,60,67,0.3);
            background-color: var(--ios-systemBackground);
            position: relative;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .ios-checkbox:checked {
            background-color: var(--ios-blue);
            border-color: var(--ios-blue);
        }
        
        .ios-checkbox:checked::after {
            content: '';
            position: absolute;
            left: 7px;
            top: 3px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        /* iOS应用Logo */
        .ios-app-icon {
            width: 72px;
            height: 72px;
            border-radius: 18px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            margin: 0 auto;
        }
        
        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: var(--ios-separator);
        }

        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">登录</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="w-16"></div>
            </div>

            <!-- 登录表单 -->
            <div class="px-6 pt-8 ios-fade-in ios-fade-in-delay-1">
                <!-- Logo和标题 -->
                <div class="text-center mb-12">
                    <div class="ios-app-icon bg-blue-50 flex items-center justify-center mb-5">
                        <i class="fas fa-home text-[#007AFF] text-3xl"></i>
                    </div>
                    <h1 class="text-xl font-bold text-[#000000]">乐享友邻</h1>
                    <p class="text-[#3C3C43B2] mt-2 text-sm">连接邻里，共享社区</p>
                </div>

                <!-- 表单 -->
                <div class="space-y-6">
                    <!-- 手机号输入 -->
                    <div class="space-y-2">
                        <label class="ios-form-label">手机号码</label>
                        <div class="relative">
                            <input type="tel" placeholder="请输入手机号码" class="ios-input ios-haptic">
                            <span class="absolute right-4 top-1/2 -translate-y-1/2">
                                <i class="fas fa-mobile-alt text-[#3C3C434D]"></i>
                            </span>
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <label class="ios-form-label">密码</label>
                            <a href="#" class="ios-link-button ios-haptic text-sm">忘记密码?</a>
                        </div>
                        <div class="relative">
                            <input type="password" placeholder="请输入密码" class="ios-input ios-haptic">
                            <button class="absolute right-4 top-1/2 -translate-y-1/2 ios-haptic">
                                <i class="fas fa-eye-slash text-[#3C3C434D]"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 记住密码 -->
                    <div class="flex items-center mt-2">
                        <input type="checkbox" id="remember" class="ios-checkbox ios-haptic">
                        <label for="remember" class="ml-2 text-sm text-[#3C3C43B2]">记住密码</label>
                    </div>

                    <!-- 登录按钮 -->
                    <button class="ios-primary-button ios-haptic mt-8" onclick="window.location.href='../community/community-select.html'">
                        登录
                    </button>

                    <!-- 其他登录方式 -->
                    <div class="mt-16 ios-fade-in ios-fade-in-delay-2">
                        <div class="flex items-center justify-center">
                            <div class="flex-1 ios-separator"></div>
                            <div class="px-4 text-sm text-[#3C3C43B2]">其他方式登录</div>
                            <div class="flex-1 ios-separator"></div>
                        </div>
                        <div class="flex justify-center space-x-8 mt-6">
                            <button class="ios-social-button ios-haptic">
                                <i class="fab fa-weixin text-[#34C759] text-2xl"></i>
                            </button>
                            <button class="ios-social-button ios-haptic">
                                <i class="fab fa-qq text-[#007AFF] text-2xl"></i>
                            </button>
                            <button class="ios-social-button ios-haptic">
                                <i class="fas fa-mobile-alt text-[#3C3C43B2] text-2xl"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 底部注册链接 -->
                <div class="mt-auto py-8 text-center ios-fade-in ios-fade-in-delay-3">
                    <p class="text-[#3C3C43B2]">还没有账号? <a href="register.html" class="ios-link-button ios-haptic">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 切换密码可见性
            const passwordToggle = document.querySelector('.fa-eye-slash').parentElement;
            const passwordInput = document.querySelector('input[type="password"]');
            
            passwordToggle.addEventListener('click', function() {
                const icon = this.querySelector('i');
                
                // 添加触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate([5, 10, 5]);
                }
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                }
            });
            
            // 登录按钮点击事件
            const loginButton = document.querySelector('.ios-primary-button');
            loginButton.addEventListener('click', function() {
                // 添加强烈的触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate([10, 20, 10]);
                }
            });
            
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.ios-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    // 轻微触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate(5);
                    }
                    
                    // 添加轻微动画效果
                    this.style.transition = 'transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    this.style.transform = 'scale(1.005)';
                });
                
                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });
            
            // 确保所有淡入元素最终显示
            setTimeout(() => {
                document.querySelectorAll('.ios-fade-in').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        });
    </script>
</body>
</html> 