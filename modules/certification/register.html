<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 注册</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-systemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        /* iOS输入框 */
        .ios-input {
            width: 100%;
            padding: 14px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid rgba(60,60,67,0.15);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            transition: all 0.2s ease;
            outline: none;
            -webkit-appearance: none;
            color: var(--ios-label);
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
        }
        
        .ios-input::placeholder {
            color: var(--ios-tertiaryLabel);
        }
        
        /* iOS表单标签 */
        .ios-form-label {
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-label);
            margin-bottom: 8px;
            display: block;
        }
        
        /* iOS主按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 14px 20px;
            width: 100%;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-primary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.8);
        }
        
        /* iOS次要按钮 */
        .ios-secondary-button {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--ios-blue);
            border-radius: var(--ios-corner-radius-medium);
            font-size: 15px;
            font-weight: 600;
            padding: 12px 16px;
            text-align: center;
            transition: all 0.15s ease;
            white-space: nowrap;
        }
        
        .ios-secondary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.15);
        }
        
        /* iOS链接按钮 */
        .ios-link-button {
            color: var(--ios-blue);
            font-size: 15px;
            font-weight: 500;
            transition: opacity 0.2s ease;
        }
        
        .ios-link-button:active {
            opacity: 0.6;
        }
        
        /* iOS复选框 */
        .ios-checkbox {
            appearance: none;
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 1px solid rgba(60,60,67,0.3);
            background-color: var(--ios-systemBackground);
            position: relative;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .ios-checkbox:checked {
            background-color: var(--ios-blue);
            border-color: var(--ios-blue);
        }
        
        .ios-checkbox:checked::after {
            content: '';
            position: absolute;
            left: 7px;
            top: 3px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        /* iOS小提示 */
        .ios-hint {
            font-size: 13px;
            color: var(--ios-tertiaryLabel);
            margin-top: 6px;
        }

        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">注册</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="window.location.href='login.html'" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="w-16"></div>
            </div>

            <!-- 注册表单 -->
            <div class="px-6 pt-6 ios-fade-in ios-fade-in-delay-1">
                <!-- 标题 -->
                <div class="mb-8">
                    <h1 class="text-xl font-bold text-[#000000]">注册账号</h1>
                    <p class="text-[#3C3C43B2] mt-2 text-sm">加入乐享友邻，享受社区便利</p>
                </div>

                <!-- 表单 -->
                <div class="space-y-5">
                    <!-- 手机号输入 -->
                    <div class="space-y-2">
                        <label class="ios-form-label">手机号码</label>
                        <div class="relative">
                            <input type="tel" placeholder="请输入手机号码" class="ios-input ios-haptic">
                            <span class="absolute right-4 top-1/2 -translate-y-1/2">
                                <i class="fas fa-mobile-alt text-[#3C3C434D]"></i>
                            </span>
                        </div>
                    </div>

                    <!-- 验证码输入 -->
                    <div class="space-y-2">
                        <label class="ios-form-label">验证码</label>
                        <div class="flex gap-3">
                            <div class="relative flex-1">
                                <input type="text" placeholder="请输入验证码" class="ios-input ios-haptic">
                            </div>
                            <button class="ios-secondary-button ios-haptic" id="verifyCodeBtn">
                                获取验证码
                            </button>
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="space-y-2">
                        <label class="ios-form-label">设置密码</label>
                        <div class="relative">
                            <input type="password" placeholder="请设置6-20位登录密码" class="ios-input ios-haptic">
                            <button class="absolute right-4 top-1/2 -translate-y-1/2 ios-haptic">
                                <i class="fas fa-eye-slash text-[#3C3C434D]"></i>
                            </button>
                        </div>
                        <p class="ios-hint">密码需包含字母、数字和特殊符号</p>
                    </div>

                    <!-- 确认密码 -->
                    <div class="space-y-2">
                        <label class="ios-form-label">确认密码</label>
                        <div class="relative">
                            <input type="password" placeholder="请再次输入密码" class="ios-input ios-haptic">
                            <button class="absolute right-4 top-1/2 -translate-y-1/2 ios-haptic">
                                <i class="fas fa-eye-slash text-[#3C3C434D]"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 用户协议 -->
                    <div class="flex items-start mt-6">
                        <input type="checkbox" id="agreement" class="ios-checkbox ios-haptic mt-0.5">
                        <label for="agreement" class="ml-2 text-sm text-[#3C3C43B2]">
                            我已阅读并同意<a href="#" class="ios-link-button ios-haptic">《用户协议》</a>和<a href="#" class="ios-link-button ios-haptic">《隐私政策》</a>
                        </label>
                    </div>

                    <!-- 注册按钮 -->
                    <button class="ios-primary-button ios-haptic mt-8">
                        注册
                    </button>
                </div>

                <!-- 底部登录链接 -->
                <div class="mt-12 text-center ios-fade-in ios-fade-in-delay-2">
                    <p class="text-[#3C3C43B2]">已有账号? <a href="login.html" class="ios-link-button ios-haptic">返回登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 切换密码可见性
            const passwordToggles = document.querySelectorAll('.fa-eye-slash');
            
            passwordToggles.forEach(icon => {
                const button = icon.parentElement;
                const input = button.parentElement.querySelector('input');
                
                button.addEventListener('click', function() {
                    // 添加触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([5, 10, 5]);
                    }
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    }
                });
            });

            // 获取验证码倒计时
            const verifyCodeButton = document.getElementById('verifyCodeBtn');
            verifyCodeButton.addEventListener('click', function() {
                let countdown = 60;
                const originalText = this.textContent.trim();
                this.disabled = true;
                this.classList.add('opacity-60');
                
                // 添加强烈的触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate([5, 15, 5]);
                }
                
                const timer = setInterval(() => {
                    countdown--;
                    this.textContent = `重新获取(${countdown}s)`;
                    
                    if (countdown <= 0) {
                        clearInterval(timer);
                        this.textContent = originalText;
                        this.disabled = false;
                        this.classList.remove('opacity-60');
                    }
                }, 1000);
            });

            // 注册按钮点击事件
            const registerButton = document.querySelector('.ios-primary-button');
            registerButton.addEventListener('click', function() {
                const agreement = document.getElementById('agreement');
                
                // 添加强烈的触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate([10, 20, 10]);
                }
                
                if (!agreement.checked) {
                    alert('请阅读并同意用户协议和隐私政策');
                    return;
                }
                
                // 这里添加注册逻辑
                alert('注册成功！');
                window.location.href = 'login.html';
            });
            
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.ios-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    // 轻微触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate(5);
                    }
                    
                    // 添加轻微动画效果
                    this.style.transition = 'transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    this.style.transform = 'scale(1.005)';
                });
                
                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });
            
            // 确保所有淡入元素最终显示
            setTimeout(() => {
                document.querySelectorAll('.ios-fade-in').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        });
    </script>
</body>
</html> 