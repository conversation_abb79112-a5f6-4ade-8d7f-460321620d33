<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 房源租赁市场</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        /* 深色模式支持 - 已注释掉，避免系统默认使用深色模式 */
        @media (prefers-color-scheme: dark) {
            body.dark-theme {
                --ios-systemBackground: #000000;
                --ios-secondarySystemBackground: #1C1C1E;
                --ios-tertiarySystemBackground: #2C2C2E;
                --ios-groupedBackground: #000000;
                --ios-card: #1C1C1E;
                
                --ios-label: #FFFFFF;
                --ios-secondaryLabel: #EBEBF599;
                --ios-tertiaryLabel: #EBEBF54D;
                --ios-quaternaryLabel: #EBEBF52E;
            }
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS搜索框样式 */
        .ios-search {
            border-radius: 10px;
            background-color: var(--ios-light-gray);
            height: 36px;
            transition: background-color 0.2s ease;
        }
        
        .ios-search:focus-within {
            background-color: #E0E0E6;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }

        /* 搜索栏容器 */
        .ios-search-container {
            position: sticky;
            top: 44px;
            z-index: 20;
            background-color: var(--ios-systemBackground);
            padding: 8px 16px;
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
        }
        
        /* 分类标签栏 */
        .ios-category-bar {
            position: sticky;
            top: 92px;
            z-index: 15;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
            padding: 8px 16px;
        }
        
        /* 分类标签按钮 */
        .ios-category-button {
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
            background-color: var(--ios-light-gray);
            color: var(--ios-secondaryLabel);
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .ios-category-button.active {
            background-color: var(--ios-blue);
            color: white;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 水波纹效果 */
        .ios-ripple-effect {
            position: relative;
            overflow: hidden;
        }

        .ios-ripple-effect::after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.4s, opacity 0.8s;
        }

        .ios-ripple-effect:active::after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-top: 4px;
            padding-bottom: 100px;
        }
        
        /* iOS房源卡片样式 */
        .ios-house-card {
            background-color: white;
            margin-bottom: 16px;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 筛选栏样式 */
        .ios-filter-bar {
            position: sticky;
            top: 140px;
            z-index: 10;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
        }
        
        .ios-filter-button {
            font-size: 13px;
            font-weight: 500;
            transition: color 0.2s ease;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">房源租赁市场</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='../navigation/home.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">房源租赁</h1>
                <div class="w-16"></div>
            </div>

            <!-- 搜索栏 -->
            <div class="ios-search-container">
                <div class="flex items-center ios-search px-3">
                    <i class="fas fa-search text-[#8E8E93] text-xs"></i>
                    <input type="text" placeholder="搜索小区名称" class="ml-2 bg-transparent flex-1 outline-none text-sm h-full">
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="ios-filter-bar">
                <div class="flex justify-around py-3 border-b border-[rgba(60,60,67,0.1)]">
                    <button class="ios-filter-button ios-button ios-haptic flex items-center text-sm text-[#3C3C43B2]">
                        <span>区域</span>
                        <i class="fas fa-chevron-down ml-1 text-[10px]"></i>
                    </button>
                    <button class="ios-filter-button ios-button ios-haptic flex items-center text-sm text-[#3C3C43B2]">
                        <span>租金</span>
                        <i class="fas fa-chevron-down ml-1 text-[10px]"></i>
                    </button>
                    <button class="ios-filter-button ios-button ios-haptic flex items-center text-sm text-[#3C3C43B2]">
                        <span>户型</span>
                        <i class="fas fa-chevron-down ml-1 text-[10px]"></i>
                    </button>
                    <button class="ios-filter-button ios-button ios-haptic flex items-center text-sm text-[#3C3C43B2]">
                        <span>筛选</span>
                        <i class="fas fa-sliders-h ml-1 text-[10px]"></i>
                    </button>
                </div>
            </div>

            <!-- 分类标签 -->
            <div class="ios-category-bar">
                <div class="flex space-x-3 overflow-x-auto ios-scroll-indicator">
                    <button class="ios-category-button active ios-button ios-haptic">整租</button>
                    <button class="ios-category-button ios-button ios-haptic">合租</button>
                    <button class="ios-category-button ios-button ios-haptic">押一付一</button>
                    <button class="ios-category-button ios-button ios-haptic">近地铁</button>
                    <button class="ios-category-button ios-button ios-haptic">精装修</button>
                    <button class="ios-category-button ios-button ios-haptic">带阳台</button>
                </div>
            </div>

            <div class="ios-content-area">
                <!-- 房源列表 -->
                <div class="px-4 mt-4">
                    <!-- 房源1 -->
                    <div class="ios-card ios-fade-in mb-4 ios-button ios-haptic ios-ripple-effect" style="animation-delay: 0.05s;">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1502672260266-1c1ef2d93688" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2 bg-black bg-opacity-50 backdrop-blur-md text-white text-xs px-2 py-1 rounded-full flex items-center">
                                <i class="fas fa-image mr-1 text-[10px]"></i><span>12张</span>
                            </div>
                        </div>
                        <div class="p-3.5">
                            <div class="flex items-start justify-between">
                                <h3 class="text-[17px] font-medium leading-tight">阳光花园 2室1厅</h3>
                                <p class="ios-price text-[17px]">¥3,500/月</p>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] mt-1">85m² | 南北通透 | 精装修</p>
                            <div class="flex flex-wrap items-center gap-2 mt-2">
                                <span class="ios-tag">整租</span>
                                <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">拎包入住</span>
                                <span class="ios-tag" style="background-color: rgba(255,149,0,0.1); color: var(--ios-orange);">近地铁</span>
                            </div>
                            <p class="text-xs text-[#8E8E93] mt-2">距离地铁2号线阳光花园站步行5分钟</p>
                        </div>
                    </div>

                    <!-- 房源2 -->
                    <div class="ios-card ios-fade-in mb-4 ios-button ios-haptic ios-ripple-effect" style="animation-delay: 0.1s;">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1502005229762-cf1b2da7c5d6" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2 bg-black bg-opacity-50 backdrop-blur-md text-white text-xs px-2 py-1 rounded-full flex items-center">
                                <i class="fas fa-image mr-1 text-[10px]"></i><span>9张</span>
                            </div>
                        </div>
                        <div class="p-3.5">
                            <div class="flex items-start justify-between">
                                <h3 class="text-[17px] font-medium leading-tight">幸福小区 3室2厅</h3>
                                <p class="ios-price text-[17px]">¥4,800/月</p>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] mt-1">120m² | 南向 | 豪华装修</p>
                            <div class="flex flex-wrap items-center gap-2 mt-2">
                                <span class="ios-tag">整租</span>
                                <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">带车位</span>
                                <span class="ios-tag" style="background-color: rgba(88,86,214,0.1); color: var(--ios-purple);">电梯房</span>
                            </div>
                            <p class="text-xs text-[#8E8E93] mt-2">小区环境优美，配套设施齐全</p>
                        </div>
                    </div>

                    <!-- 房源3 -->
                    <div class="ios-card ios-fade-in mb-4 ios-button ios-haptic ios-ripple-effect" style="animation-delay: 0.15s;">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1502005097973-6a7082348e28" class="w-full h-48 object-cover">
                            <div class="absolute top-2 right-2 bg-black bg-opacity-50 backdrop-blur-md text-white text-xs px-2 py-1 rounded-full flex items-center">
                                <i class="fas fa-image mr-1 text-[10px]"></i><span>8张</span>
                            </div>
                        </div>
                        <div class="p-3.5">
                            <div class="flex items-start justify-between">
                                <h3 class="text-[17px] font-medium leading-tight">和谐家园 1室1厅</h3>
                                <p class="ios-price text-[17px]">¥2,000/月</p>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] mt-1">45m² | 东向 | 简装</p>
                            <div class="flex flex-wrap items-center gap-2 mt-2">
                                <span class="ios-tag" style="background-color: rgba(88,86,214,0.1); color: var(--ios-purple);">合租</span>
                                <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">押一付一</span>
                                <span class="ios-tag" style="background-color: rgba(0,122,255,0.1); color: var(--ios-blue);">独立卫浴</span>
                            </div>
                            <p class="text-xs text-[#8E8E93] mt-2">适合单身白领，周边配套成熟</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-1.5 pb-6 z-40">
                <button onclick="window.location.href='../navigation/home.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">首页</span>
                </button>
                <button onclick="window.location.href='../navigation/discover.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">发现</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="window.location.href='../navigation/messages.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">消息</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 分类标签切换
            const categoryButtons = document.querySelectorAll('.ios-category-button');
            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    categoryButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 