<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 房源详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        /* 深色模式支持 - 已注释掉，避免系统默认使用深色模式 */
        @media (prefers-color-scheme: dark) {
            body.dark-theme {
                --ios-systemBackground: #000000;
                --ios-secondarySystemBackground: #1C1C1E;
                --ios-tertiarySystemBackground: #2C2C2E;
                --ios-groupedBackground: #000000;
                --ios-card: #1C1C1E;
                
                --ios-label: #FFFFFF;
                --ios-secondaryLabel: #EBEBF599;
                --ios-tertiaryLabel: #EBEBF54D;
                --ios-quaternaryLabel: #EBEBF52E;
            }
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 17px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 水波纹效果 */
        .ios-ripple-effect {
            position: relative;
            overflow: hidden;
        }

        .ios-ripple-effect::after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.4s, opacity 0.8s;
        }

        .ios-ripple-effect:active::after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-bottom: 100px;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 详情项样式 */
        .detail-item {
            display: flex;
            margin-bottom: 16px;
        }
        
        .detail-label {
            width: 96px;
            color: var(--ios-secondaryLabel);
            font-size: 15px;
        }
        
        .detail-value {
            flex: 1;
            font-size: 15px;
        }
        
        /* 设施图标样式 */
        .facility-icon {
            width: 56px;
            height: 56px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        /* 底部固定按钮 */
        .ios-fixed-btn {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 10px;
            font-weight: 500;
            font-size: 15px;
            padding: 12px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">房源详情</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='house-market.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">房源详情</h1>
                <div class="flex space-x-5">
                    <button class="ios-button ios-haptic">
                        <i class="fas fa-share-alt text-[#007AFF]"></i>
                    </button>
                    <button class="ios-button ios-haptic">
                        <i class="far fa-heart text-[#007AFF]"></i>
                    </button>
                </div>
            </div>

            <div class="ios-content-area">
                <!-- 房源图片轮播 -->
                <div class="relative ios-fade-in">
                    <div class="aspect-video bg-[#F2F2F7]">
                        <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267" class="w-full h-full object-cover">
                    </div>
                    <div class="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white text-xs px-3 py-1 rounded-full backdrop-blur-md">
                        1/6
                    </div>
                </div>

                <!-- 房源信息 -->
                <div class="bg-white p-4 space-y-3 ios-fade-in" style="animation-delay: 0.05s;">
                    <h1 class="text-xl font-semibold tracking-tight">阳光花园 2室1厅 南北通透</h1>
                    <div class="flex items-center text-sm space-x-4">
                        <span class="text-2xl font-semibold ios-price">¥3,200</span>
                        <span class="text-[#8E8E93]">押一付三</span>
                    </div>
                    <div class="flex items-center text-sm space-x-4 text-[#8E8E93]">
                        <span>80㎡</span>
                        <span>南北通透</span>
                        <span>5/18层</span>
                    </div>
                </div>

                <!-- 房屋详情 -->
                <div class="bg-white mt-2 p-4 ios-fade-in" style="animation-delay: 0.1s;">
                    <h2 class="ios-section-title mb-4">房屋详情</h2>
                    <div class="space-y-4">
                        <div class="detail-item">
                            <span class="detail-label">房屋类型</span>
                            <span class="detail-value">2室1厅1卫</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">装修情况</span>
                            <span class="detail-value">精装修</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">朝向楼层</span>
                            <span class="detail-value">南北通透 | 8层</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">入住时间</span>
                            <span class="detail-value">随时入住</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">房屋配置</span>
                            <div class="detail-value">
                                <div class="grid grid-cols-4 gap-2">
                                    <div class="facility-icon">
                                        <i class="fas fa-bed text-[#007AFF] mb-1 text-lg"></i>
                                        <span class="text-xs">床</span>
                                    </div>
                                    <div class="facility-icon">
                                        <i class="fas fa-wind text-[#007AFF] mb-1 text-lg"></i>
                                        <span class="text-xs">空调</span>
                                    </div>
                                    <div class="facility-icon">
                                        <i class="fas fa-tv text-[#007AFF] mb-1 text-lg"></i>
                                        <span class="text-xs">电视</span>
                                    </div>
                                    <div class="facility-icon">
                                        <i class="fas fa-wifi text-[#007AFF] mb-1 text-lg"></i>
                                        <span class="text-xs">宽带</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">房屋照片</span>
                            <div class="detail-value">
                                <div class="grid grid-cols-4 gap-2">
                                    <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267" class="w-full h-16 object-cover rounded-md">
                                    <img src="https://images.unsplash.com/photo-1502672260266-1c1ef2d93688" class="w-full h-16 object-cover rounded-md">
                                    <img src="https://images.unsplash.com/photo-1484154218962-a197022b5858" class="w-full h-16 object-cover rounded-md">
                                    <img src="https://images.unsplash.com/photo-1493809842364-78817add7ffb" class="w-full h-16 object-cover rounded-md">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 位置信息 -->
                <div class="bg-white mt-2 p-4 ios-fade-in" style="animation-delay: 0.15s;">
                    <h2 class="ios-section-title mb-4">位置及周边</h2>
                    <div class="h-40 bg-[#F2F2F7] rounded-lg mb-4 relative">
                        <img src="https://images.unsplash.com/photo-1524661135-423995f22d0b" class="w-full h-full object-cover rounded-lg">
                        <div class="absolute bottom-4 right-4">
                            <button class="bg-[#007AFF] text-white px-4 py-2 rounded-lg text-sm ios-button ios-haptic">
                                <i class="fas fa-location-arrow mr-1"></i>查看地图
                            </button>
                        </div>
                    </div>
                    <div class="detail-item">
                        <span class="detail-value text-[15px] text-[#3C3C43B2]">北京市朝阳区阳光花园2号楼 8层</span>
                    </div>
                    <div class="space-y-3 mt-3">
                        <div class="flex items-center text-[13px]">
                            <i class="fas fa-subway text-[#007AFF] w-5"></i>
                            <span class="text-[#3C3C43B2]">距离14号线东段望京南站步行800米</span>
                        </div>
                        <div class="flex items-center text-[13px]">
                            <i class="fas fa-bus text-[#007AFF] w-5"></i>
                            <span class="text-[#3C3C43B2]">公交站：望京南站、阳光花园站</span>
                        </div>
                        <div class="flex items-center text-[13px]">
                            <i class="fas fa-shopping-bag text-[#007AFF] w-5"></i>
                            <span class="text-[#3C3C43B2]">购物：永辉超市（400米）、华联商厦（800米）</span>
                        </div>
                        <div class="flex items-center text-[13px]">
                            <i class="fas fa-utensils text-[#007AFF] w-5"></i>
                            <span class="text-[#3C3C43B2]">餐饮：多家餐厅、咖啡厅在步行10分钟范围内</span>
                        </div>
                        <div class="flex items-center text-[13px]">
                            <i class="fas fa-hospital text-[#007AFF] w-5"></i>
                            <span class="text-[#3C3C43B2]">医疗：望京医院（1.2公里）、社区诊所（300米）</span>
                        </div>
                        <div class="flex items-center text-[13px]">
                            <i class="fas fa-graduation-cap text-[#007AFF] w-5"></i>
                            <span class="text-[#3C3C43B2]">教育：阳光小学（500米）、实验中学（1.5公里）</span>
                        </div>
                    </div>
                </div>                

                <!-- 房东信息 -->
                <div class="mt-2 bg-white p-4 ios-fade-in" style="animation-delay: 0.2s;">
                    <h2 class="ios-section-title mb-4">房东信息</h2>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="w-12 h-12 rounded-full object-cover border border-gray-100">
                            <div>
                                <h3 class="text-[17px] font-medium">张房东</h3>
                                <div class="flex items-center text-xs text-[#8E8E93] mt-1">
                                    <i class="fas fa-star text-[#FF9500]"></i>
                                    <span class="ml-1">4.8</span>
                                    <span class="mx-2">|</span>
                                    <span>已出租 12套</span>
                                </div>
                            </div>
                        </div>
                        <button class="px-4 py-2 border border-[#007AFF] text-[#007AFF] rounded-full text-sm ios-button ios-haptic">
                            联系房东
                        </button>
                    </div>
                </div>
                
                <!-- 联系信息 -->
                <div class="mt-2 bg-white p-4 ios-fade-in" style="animation-delay: 0.25s;">
                    <h2 class="ios-section-title mb-4">联系信息</h2>
                    <div class="space-y-4">
                        <div class="detail-item">
                            <span class="detail-label">联系人</span>
                            <span class="detail-value">张先生（房东）</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">联系电话</span>
                            <div class="detail-value flex items-center">
                                <span class="mr-3">138****6789</span>
                                <button class="text-[#007AFF] ios-button ios-haptic flex items-center">
                                    <i class="fas fa-phone-alt mr-1"></i>
                                    <span>拨打电话</span>
                                </button>
                            </div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">微信</span>
                            <div class="detail-value flex items-center">
                                <span class="mr-3">house_zhang</span>
                                <button class="text-[#007AFF] ios-button ios-haptic flex items-center">
                                    <i class="fas fa-copy mr-1"></i>
                                    <span>复制微信号</span>
                                </button>
                            </div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">看房时间</span>
                            <span class="detail-value">周一至周日 9:00-20:00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部固定按钮 -->
            <div class="fixed bottom-0 left-0 right-0 p-4 ios-bottom-nav z-40">
                <button class="ios-fixed-btn ios-button ios-haptic">立即预约看房</button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 