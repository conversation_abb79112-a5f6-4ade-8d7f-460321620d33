<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>拼单详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS列表分割线样式 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin-left: 16px;
        }
        
        /* 进度条样式 */
        .progress-bar {
            height: 6px;
            background-color: var(--ios-light-gray);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--ios-green);
        }

        /* 拼单计时器 */
        .countdown-timer {
            font-variant-numeric: tabular-nums;
            font-weight: 500;
            color: var(--ios-blue);
        }

        /* 底部操作区 */
        .bottom-action-area {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 12px 16px;
            padding-bottom: calc(12px + env(safe-area-inset-bottom));
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            z-index: 50;
        }
        
        /* 主按钮 */
        .primary-button {
            background-color: var(--ios-blue);
            color: white;
            font-weight: 600;
            border-radius: 10px;
            padding: 12px 0;
            text-align: center;
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .primary-button:active {
            transform: scale(0.98);
            background-color: #0062CC;
        }
        
        /* 图片预览 */
        .image-preview {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 12px;
        }
        
        .preview-item {
            border-radius: 8px;
            overflow: hidden;
            aspect-ratio: 1/1;
        }
        
        /* 参与者列表 */
        .participant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            overflow: hidden;
            border: 2px solid white;
            background-color: #F2F2F7;
        }
        
        /* 倒计时样式 */
        .large-countdown {
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
            font-variant-numeric: tabular-nums;
            border-radius: 8px;
            padding: 8px 12px;
        }

        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }

        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator pb-24">
            <div class="screen-title">拼单详情</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='group-buy-home.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">拼单详情</h1>
                <button onclick="shareGroupBuy()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-share-alt text-[#007AFF]"></i>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="p-4">
                <!-- 拼单基本信息 -->
                <div class="ios-card p-4 mb-4">
                    <!-- 发起人信息 -->
                    <div class="flex items-start justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1494790108377-be9c29b29330')"></div>
                            <div class="ml-2">
                                <div class="text-[15px] font-medium">王小花</div>
                                <div class="text-xs text-[#8E8E93]">5栋2单元 · 刚刚发布</div>
                            </div>
                        </div>
                        <div class="ios-tag bg-[rgba(0,122,255,0.1)] text-[#007AFF]">超市果蔬</div>
                    </div>
                    
                    <!-- 拼单标题和描述 -->
                    <h1 class="text-xl font-semibold mt-4">盒马鲜生水果拼单</h1>
                    
                    <!-- 倒计时 -->
                    <div class="large-countdown flex items-center justify-center mt-3 text-base font-medium">
                        <span>距结束：</span>
                        <span class="ml-2">23小时45分30秒</span>
                    </div>
                    
                    <!-- 拼单进度 -->
                    <div class="mt-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-sm">已有2人参与，还差3人</span>
                            <span class="text-sm font-medium">2/5人</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 40%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 拼单详情 -->
                <div class="ios-card p-4 mb-4">
                    <h2 class="text-base font-semibold mb-3">拼单详情</h2>
                    <p class="text-[15px] leading-relaxed text-[#3C3C43CC]">
                        本周末去盒马采购水果，有需要带的吗？车厘子、葡萄、苹果、橙子都可以，满300免运费~
                        <br><br>
                        拼单时间：2024年3月25日上午10点<br>
                        集合地点：小区北门口<br>
                        预计费用：人均100-200元<br>
                        支付方式：微信或支付宝<br>
                        <br>
                        特别说明：请提前告知需要什么水果和大致数量，方便统一采购。有特殊需求可以加微信单独沟通。
                    </p>
                    
                    <!-- 图片展示 -->
                    <div class="image-preview">
                        <div class="preview-item">
                            <img src="https://images.unsplash.com/photo-1528821128474-27f963b062bf" class="w-full h-full object-cover">
                        </div>
                        <div class="preview-item">
                            <img src="https://images.unsplash.com/photo-1561043433-9265f73e685f" class="w-full h-full object-cover">
                        </div>
                    </div>
                </div>
                
                <!-- 参与者信息 -->
                <div class="ios-card p-4 mb-4">
                    <h2 class="text-base font-semibold mb-3">参与者 (2/5)</h2>
                    
                    <!-- 参与者列表 -->
                    <div class="flex flex-wrap">
                        <!-- 发起人 -->
                        <div class="flex flex-col items-center mr-4 mb-3">
                            <div class="participant-avatar relative">
                                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" class="w-full h-full object-cover">
                                <div class="absolute bottom-0 right-0 w-4 h-4 bg-[#007AFF] rounded-full flex items-center justify-center">
                                    <i class="fas fa-crown text-white text-[8px]"></i>
                                </div>
                            </div>
                            <span class="text-xs mt-1">王小花</span>
                            <span class="text-[10px] text-[#8E8E93]">发起人</span>
                        </div>
                        
                        <!-- 其他参与者 -->
                        <div class="flex flex-col items-center mr-4 mb-3">
                            <div class="participant-avatar">
                                <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb" class="w-full h-full object-cover">
                            </div>
                            <span class="text-xs mt-1">李明</span>
                            <span class="text-[10px] text-[#8E8E93]">3栋1单元</span>
                        </div>
                        
                        <!-- 空位展示 -->
                        <div class="flex flex-col items-center mr-4 mb-3">
                            <div class="participant-avatar flex items-center justify-center border-dashed border-2 border-[#E5E5EA]">
                                <i class="fas fa-plus text-[#8E8E93]"></i>
                            </div>
                            <span class="text-xs mt-1">待加入</span>
                        </div>
                        
                        <div class="flex flex-col items-center mr-4 mb-3">
                            <div class="participant-avatar flex items-center justify-center border-dashed border-2 border-[#E5E5EA]">
                                <i class="fas fa-plus text-[#8E8E93]"></i>
                            </div>
                            <span class="text-xs mt-1">待加入</span>
                        </div>
                        
                        <div class="flex flex-col items-center mr-4 mb-3">
                            <div class="participant-avatar flex items-center justify-center border-dashed border-2 border-[#E5E5EA]">
                                <i class="fas fa-plus text-[#8E8E93]"></i>
                            </div>
                            <span class="text-xs mt-1">待加入</span>
                        </div>
                    </div>
                </div>
                
                <!-- 联系方式 -->
                <div class="ios-card p-4 mb-4">
                    <h2 class="text-base font-semibold mb-3">联系方式</h2>
                    
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-phone text-[#8E8E93] w-5"></i>
                            <span class="ml-2">138****5678</span>
                            <button class="ml-auto bg-[rgba(0,122,255,0.1)] px-2 py-1 rounded-md text-[#007AFF] text-sm ios-button ios-haptic">
                                拨打
                            </button>
                        </div>
                        
                        <div class="flex items-center">
                            <i class="fab fa-weixin text-[#8E8E93] w-5"></i>
                            <span class="ml-2">xiaohuar123</span>
                            <button class="ml-auto bg-[rgba(0,122,255,0.1)] px-2 py-1 rounded-md text-[#007AFF] text-sm ios-button ios-haptic">
                                复制
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 拼单规则 -->
                <div class="ios-card p-4 mb-4">
                    <h2 class="text-base font-semibold mb-3">拼单须知</h2>
                    
                    <ul class="text-sm text-[#3C3C43CC] list-disc list-inside space-y-2">
                        <li>参与拼单前，请确保已阅读并同意拼单规则</li>
                        <li>拼单成功后，请按时参与活动</li>
                        <li>如需取消，请提前24小时通知发起人</li>
                        <li>发起人有权拒绝参与者加入</li>
                        <li>拼单结束后，请及时确认并支付相关费用</li>
                    </ul>
                </div>
            </div>
            
            <!-- 底部操作区 -->
            <div class="bottom-action-area flex">
                <button class="ios-button ios-haptic flex items-center justify-center w-12 h-12">
                    <i class="far fa-star text-lg text-[#007AFF]"></i>
                </button>
                <button class="ios-button ios-haptic flex items-center justify-center w-12 h-12 ml-2">
                    <i class="far fa-comment-dots text-lg text-[#8E8E93]"></i>
                </button>
                <button class="primary-button ios-button ios-haptic flex-1 text-base ml-3">
                    参与拼单
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-button');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
        
        // 分享拼单
        function shareGroupBuy() {
            alert('分享拼单功能');
        }
    </script>
</body>
</html> 