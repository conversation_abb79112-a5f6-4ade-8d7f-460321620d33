<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>发布拼单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS表单元素样式 */
        .ios-form-group {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .ios-form-row {
            display: flex;
            padding: 12px 16px;
            border-bottom: 0.5px solid rgba(60,60,67,0.1);
            align-items: center;
        }
        
        .ios-form-row:last-child {
            border-bottom: none;
        }
        
        .ios-form-label {
            font-size: 15px;
            width: 100px;
            flex-shrink: 0;
        }
        
        .ios-input {
            flex: 1;
            border: none;
            font-size: 15px;
            outline: none;
            background: transparent;
        }
        
        .ios-textarea {
            width: 100%;
            border: none;
            font-size: 15px;
            resize: none;
            outline: none;
            height: 100px;
            background: transparent;
        }
        
        /* iOS主按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            font-weight: 600;
            font-size: 16px;
            border-radius: 10px;
            padding: 12px 0;
            text-align: center;
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .ios-primary-button:active {
            background-color: #0062CC;
            transform: scale(0.98);
        }
        
        /* 图片上传区域 */
        .upload-area {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .upload-item {
            aspect-ratio: 1/1;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }
        
        .upload-placeholder {
            border: 1px dashed rgba(60,60,67,0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: rgba(60,60,67,0.03);
        }

        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }

        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">发布拼单</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='group-buy-home.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">发布拼单</h1>
                <button onclick="window.location.href='../navigation/home.html'" class="ios-button ios-haptic">
                    <span class="text-[#007AFF]">取消</span>
                </button>
            </div>

            <div class="p-4">
                <!-- 拼单信息表单 -->
                <div class="ios-form-group">
                    <div class="ios-form-row">
                        <label class="ios-form-label">拼单标题</label>
                        <input type="text" class="ios-input" placeholder="请输入拼单标题">
                    </div>
                    
                    <div class="ios-form-row">
                        <label class="ios-form-label">拼单类型</label>
                        <select class="ios-input">
                            <option>请选择类型</option>
                            <option>超市果蔬</option>
                            <option>生鲜海鲜</option>
                            <option>电商商品</option>
                            <option>餐饮外卖</option>
                            <option>其他</option>
                        </select>
                    </div>
                    
                    <div class="ios-form-row">
                        <label class="ios-form-label">参与人数</label>
                        <input type="number" class="ios-input" placeholder="请输入所需参与人数" value="5">
                        <span class="text-[#8E8E93] ml-1">人</span>
                    </div>
                    
                    <div class="ios-form-row">
                        <label class="ios-form-label">截止时间</label>
                        <input type="text" class="ios-input" placeholder="请选择截止时间" value="2024-03-25 18:00">
                        <i class="fas fa-chevron-right text-[#C7C7CC] text-xs"></i>
                    </div>
                </div>
                
                <!-- 详细描述 -->
                <div class="ios-form-group">
                    <div class="ios-form-row" style="align-items: flex-start; padding-top: 14px;">
                        <label class="ios-form-label" style="padding-top: 2px;">拼单描述</label>
                        <textarea class="ios-textarea" placeholder="详细描述拼单商品、价格、时间安排、联系方式等信息"></textarea>
                    </div>
                </div>
                
                <!-- 图片上传 -->
                <div class="ios-form-group">
                    <div class="ios-form-row" style="display: block;">
                        <div class="mb-3">上传图片（最多4张）</div>
                        <div class="upload-area">
                            <div class="upload-placeholder upload-item ios-button">
                                <i class="fas fa-camera text-[#8E8E93] mb-1"></i>
                                <span class="text-xs text-[#8E8E93]">添加</span>
                            </div>
                            <div class="upload-item" style="display: none;">
                                <img src="" alt="" class="w-full h-full object-cover">
                                <button class="absolute top-1 right-1 bg-black bg-opacity-50 w-5 h-5 rounded-full flex items-center justify-center ios-button">
                                    <i class="fas fa-times text-white text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 联系方式 -->
                <div class="ios-form-group">
                    <div class="ios-form-row">
                        <label class="ios-form-label">联系电话</label>
                        <input type="tel" class="ios-input" placeholder="请输入联系电话">
                    </div>
                    
                    <div class="ios-form-row">
                        <label class="ios-form-label">微信号</label>
                        <input type="text" class="ios-input" placeholder="请输入微信号">
                    </div>
                </div>
                
                <!-- 提交按钮 -->
                <button onclick="window.location.href='group-buy-home.html'" class="ios-primary-button ios-button mb-8">
                    发布拼单
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-button');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 