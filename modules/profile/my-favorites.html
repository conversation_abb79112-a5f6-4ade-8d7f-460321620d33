<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 我的收藏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 12px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }

        /* iOS标签样式 */
        .ios-tab-bar {
            display: flex;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid rgba(60,60,67,0.08);
            padding: 0;
            margin: 0 0 16px;
            overflow-x: auto;
            scroll-behavior: smooth;
            position: relative;
            -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
            scrollbar-width: none; /* 隐藏滚动条 */
            -ms-overflow-style: none; /* 隐藏滚动条 */
        }
        
        .ios-tab-bar::-webkit-scrollbar {
            display: none; /* 隐藏滚动条 */
        }
        
        .ios-tab {
            flex: 1;
            min-width: 80px;
            text-align: center;
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-secondaryLabel);
            padding: 12px 0;
            position: relative;
            transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            white-space: nowrap;
        }
        
        .ios-tab.active {
            color: var(--ios-blue);
            font-weight: 600;
        }
        
        .ios-tab-indicator-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: transparent;
            overflow: hidden;
        }
        
        .ios-tab-indicator {
            position: absolute;
            bottom: 0;
            height: 2px;
            width: 24px; /* 固定宽度 */
            background-color: var(--ios-blue);
            border-radius: 1px;
            transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1); /* 更加精细的iOS动画曲线 */
            left: 50%;
            transform: translateX(-50%) scaleX(0);
            transform-origin: center;
            opacity: 0;
        }
        
        .ios-tab.active .ios-tab-indicator {
            opacity: 1;
            transform: translateX(-50%) scaleX(1);
        }

        /* iOS分段控制器 - 保留但不使用，与新的标签栏保持一致性 */
        .ios-segment-control {
            display: none; /* 隐藏旧控件 */
        }
        
        /* iOS收藏项目卡片 */
        .ios-favorite-item {
            display: flex;
            padding: 12px;
            background-color: white;
            border-radius: 12px;
            margin-bottom: 12px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
            overflow: hidden;
            transition: transform 0.2s ease;
        }
        
        .ios-favorite-item:active {
            transform: scale(0.98);
        }
        
        .ios-favorite-image {
            width: 88px;
            height: 88px;
            border-radius: 8px;
            overflow: hidden;
            background-color: var(--ios-secondarySystemBackground);
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .ios-favorite-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .ios-favorite-title {
            font-weight: 500;
            font-size: 15px;
            line-height: 1.3;
            margin-bottom: 4px;
            margin-right: 24px;
        }
        
        .ios-favorite-price {
            color: var(--ios-red);
            font-weight: 600;
            font-size: 15px;
            position: absolute;
            top: 12px;
            right: 12px;
        }
        
        .ios-favorite-desc {
            font-size: 13px;
            color: var(--ios-secondaryLabel);
            margin-bottom: 8px;
        }
        
        .ios-favorite-footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .ios-favorite-time {
            font-size: 12px;
            color: var(--ios-tertiaryLabel);
        }
        
        .ios-favorite-btn {
            color: var(--ios-blue);
            font-size: 13px;
            font-weight: 500;
        }
        
        /* 空白状态 */
        .ios-empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            opacity: 0.8;
        }
        
        .ios-empty-icon {
            width: 64px;
            height: 64px;
            background-color: var(--ios-light-gray);
            border-radius: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            color: var(--ios-gray);
            font-size: 28px;
        }
        
        .ios-empty-text {
            font-size: 15px;
            color: var(--ios-secondaryLabel);
            text-align: center;
            max-width: 200px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen ios-scroll-indicator smooth-scroll">
            <div class="screen-title">我的收藏</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">我的收藏</h1>
                <div></div>
            </div>

            <!-- 分类标签 -->
            <div class="ios-tab-bar">
                <button class="ios-tab active ios-haptic" data-tab="all">
                    全部
                    <div class="ios-tab-indicator"></div>
                </button>
                <button class="ios-tab ios-haptic" data-tab="second-hand">
                    二手闲置
                    <div class="ios-tab-indicator"></div>
                </button>
                <button class="ios-tab ios-haptic" data-tab="parking">
                    停车位
                    <div class="ios-tab-indicator"></div>
                </button>
                <button class="ios-tab ios-haptic" data-tab="housing">
                    房源
                    <div class="ios-tab-indicator"></div>
                </button>
                <div class="ios-tab-indicator-container" id="tabIndicatorContainer"></div>
            </div>

            <!-- 收藏列表 -->
            <div class="px-4 pb-32">
                <div class="ios-favorite-list">
                    <!-- 全部类别内容 -->
                    <div class="content-section active" id="all-content">
                        <!-- 二手闲置收藏 -->
                        <div class="ios-favorite-item ios-haptic ios-fade-in">
                            <div class="ios-favorite-image">
                                <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-favorite-content">
                                <div class="relative">
                                    <div class="ios-favorite-title">Nike Air Max 运动鞋</div>
                                    <div class="ios-favorite-price">¥299</div>
                                    <div class="ios-favorite-desc">9成新 | 尺码42</div>
                                </div>
                                <div class="ios-favorite-footer">
                                    <div class="ios-favorite-time">收藏于 01-15</div>
                                    <button class="ios-favorite-btn ios-haptic">查看详情</button>
                                </div>
                            </div>
                        </div>

                        <!-- 停车位收藏 -->
                        <div class="ios-favorite-item ios-haptic ios-fade-in" style="animation-delay: 0.1s;">
                            <div class="ios-favorite-image">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-favorite-content">
                                <div class="relative">
                                    <div class="ios-favorite-title">阳光小区地下车位</div>
                                    <div class="ios-favorite-price">¥300/月</div>
                                    <div class="ios-favorite-desc">朝阳区 | 可短租</div>
                                </div>
                                <div class="ios-favorite-footer">
                                    <div class="ios-favorite-time">收藏于 01-18</div>
                                    <button class="ios-favorite-btn ios-haptic">查看详情</button>
                                </div>
                            </div>
                        </div>

                        <!-- 房屋收藏 -->
                        <div class="ios-favorite-item ios-haptic ios-fade-in" style="animation-delay: 0.15s;">
                            <div class="ios-favorite-image">
                                <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-favorite-content">
                                <div class="relative">
                                    <div class="ios-favorite-title">阳光花园 2室1厅</div>
                                    <div class="ios-favorite-price">¥3200/月</div>
                                    <div class="ios-favorite-desc">80㎡ | 南北通透</div>
                                </div>
                                <div class="ios-favorite-footer">
                                    <div class="ios-favorite-time">收藏于 01-20</div>
                                    <button class="ios-favorite-btn ios-haptic">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 二手闲置类别内容 -->
                    <div class="content-section hidden" id="second-hand-content">
                        <!-- 二手闲置收藏 -->
                        <div class="ios-favorite-item ios-haptic ios-fade-in">
                            <div class="ios-favorite-image">
                                <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-favorite-content">
                                <div class="relative">
                                    <div class="ios-favorite-title">Nike Air Max 运动鞋</div>
                                    <div class="ios-favorite-price">¥299</div>
                                    <div class="ios-favorite-desc">9成新 | 尺码42</div>
                                </div>
                                <div class="ios-favorite-footer">
                                    <div class="ios-favorite-time">收藏于 01-15</div>
                                    <button class="ios-favorite-btn ios-haptic">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 停车位类别内容 -->
                    <div class="content-section hidden" id="parking-content">
                        <!-- 停车位收藏 -->
                        <div class="ios-favorite-item ios-haptic ios-fade-in">
                            <div class="ios-favorite-image">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-favorite-content">
                                <div class="relative">
                                    <div class="ios-favorite-title">阳光小区地下车位</div>
                                    <div class="ios-favorite-price">¥300/月</div>
                                    <div class="ios-favorite-desc">朝阳区 | 可短租</div>
                                </div>
                                <div class="ios-favorite-footer">
                                    <div class="ios-favorite-time">收藏于 01-18</div>
                                    <button class="ios-favorite-btn ios-haptic">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 房屋类别内容 -->
                    <div class="content-section hidden" id="house-content">
                        <!-- 房屋收藏 -->
                        <div class="ios-favorite-item ios-haptic ios-fade-in">
                            <div class="ios-favorite-image">
                                <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-favorite-content">
                                <div class="relative">
                                    <div class="ios-favorite-title">阳光花园 2室1厅</div>
                                    <div class="ios-favorite-price">¥3200/月</div>
                                    <div class="ios-favorite-desc">80㎡ | 南北通透</div>
                                </div>
                                <div class="ios-favorite-footer">
                                    <div class="ios-favorite-time">收藏于 01-20</div>
                                    <button class="ios-favorite-btn ios-haptic">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 增强标签切换功能
            const tabBar = document.querySelector('.ios-tab-bar');
            const tabs = document.querySelectorAll('.ios-tab');
            const contentSections = document.querySelectorAll('.content-section');
            const indicatorContainer = document.getElementById('tabIndicatorContainer');
            
            // 创建活动指示器
            const activeIndicator = document.createElement('div');
            activeIndicator.className = 'ios-tab-indicator';
            indicatorContainer.appendChild(activeIndicator);
            
            // 更新指示器位置的函数
            function updateIndicator(tab) {
                const tabRect = tab.getBoundingClientRect();
                const tabBarRect = tabBar.getBoundingClientRect();
                const indicatorWidth = 24; // 与CSS中定义的宽度相同
                
                // 计算指示器位置，相对于tab-bar
                const indicatorLeft = tabRect.left - tabBarRect.left + (tabRect.width - indicatorWidth) / 2;
                
                // 设置指示器位置和透明度
                activeIndicator.style.width = `${indicatorWidth}px`;
                activeIndicator.style.left = `${indicatorLeft + indicatorWidth/2}px`;
                activeIndicator.style.transform = 'translateX(-50%) scaleX(1)';
                activeIndicator.style.opacity = '1';
                
                // 确保选中的标签在可视区域中
                const scrollLeft = tabBar.scrollLeft;
                const tabOffsetLeft = tabRect.left - tabBarRect.left + scrollLeft;
                const scrollCenter = tabOffsetLeft - (tabBarRect.width / 2) + (tabRect.width / 2);
                
                if (tabOffsetLeft < scrollLeft || tabOffsetLeft + tabRect.width > scrollLeft + tabBarRect.width) {
                    tabBar.scrollTo({
                        left: scrollCenter,
                        behavior: 'smooth'
                    });
                }
            }
            
            // 初始化指示器位置
            setTimeout(() => {
                const activeTab = document.querySelector('.ios-tab.active');
                if (activeTab) {
                    updateIndicator(activeTab);
                }
            }, 100);
            
            // 新的标签切换处理
            tabs.forEach((tab, index) => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    tabs.forEach(t => {
                        t.classList.remove('active');
                        t.querySelector('.ios-tab-indicator').style.opacity = '0';
                    });
                    
                    // 给当前标签添加active类
                    this.classList.add('active');
                    
                    // 更新指示器位置
                    updateIndicator(this);
                    
                    // 切换内容
                    contentSections.forEach(section => section.classList.add('hidden'));
                    contentSections[index].classList.remove('hidden');
                    
                    // 内容区域元素重新触发动画
                    const items = contentSections[index].querySelectorAll('.ios-fade-in');
                    items.forEach((item, i) => {
                        item.style.animation = 'none';
                        setTimeout(() => {
                            item.style.animation = '';
                            item.style.animationDelay = `${0.05 * i}s`;
                        }, 10);
                    });
                });
            });
            
            // 监听窗口大小变化，更新指示器位置
            window.addEventListener('resize', () => {
                const activeTab = document.querySelector('.ios-tab.active');
                if (activeTab) {
                    updateIndicator(activeTab);
                }
            });
            
            // 保留旧的分段控制器代码但不使用它
            // 初始化分段控制器
            const segmentBtns = document.querySelectorAll('.ios-segment-btn');
            const segmentSlider = document.getElementById('segmentSlider');
            
            // 设置初始滑块宽度
            if (segmentSlider) {
                segmentSlider.style.width = (100 / segmentBtns.length) + '%';
            }
        });
    </script>
</body>
</html> 