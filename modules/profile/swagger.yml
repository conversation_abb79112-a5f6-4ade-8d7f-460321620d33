openapi: 3.0.3
info:
  title: 乐享友邻 - API Documentation
  description: 乐享友邻社区应用的完整后端接口文档
  version: 1.0.0

servers:
  - url: http://localhost:3000/api/v1
    description: 本地开发环境

security:
  - BearerAuth: []

tags:
  - name: User Profile
    description: 用户个人信息管理
  - name: User Verification
    description: 用户认证相关
  - name: User Communities
    description: 用户小区管理
  - name: User Content
    description: 用户内容管理（发布、收藏、历史）
  - name: User Notifications
    description: 消息通知
  - name: Communities
    description: 小区管理

paths:
  # ==================== User Profile APIs ====================
  /user/profile:
    get:
      tags:
        - User Profile
      summary: 获取用户基本信息
      description: 获取当前登录用户的基本信息，包括头像、昵称、用户ID、个人简介等
      responses:
        '200':
          description: 成功获取用户信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

    put:
      tags:
        - User Profile
      summary: 更新用户基本信息
      description: 更新用户的基本信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserProfileRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/statistics:
    get:
      tags:
        - User Profile
      summary: 获取用户统计数据
      description: 获取用户的发布数、收藏数、关注数、粉丝数等统计信息
      responses:
        '200':
          description: 成功获取统计数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserStatisticsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/verification:
    get:
      tags:
        - User Verification
      summary: 获取用户认证状态
      description: 获取用户的认证状态和认证类型（业主、租户、物业等）
      responses:
        '200':
          description: 成功获取认证状态
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserVerificationResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/avatar:
    post:
      tags:
        - User Profile
      summary: 上传用户头像
      description: 上传并更新用户头像
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                avatar:
                  type: string
                  format: binary
                  description: 头像图片文件
      responses:
        '200':
          description: 头像上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvatarUploadResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # ==================== User Communities APIs ====================
  /user/communities:
    get:
      tags:
        - User Communities
      summary: 获取用户小区列表
      description: 获取用户关联的所有小区列表信息，包括当前小区和其他小区
      responses:
        '200':
          description: 成功获取小区列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCommunitiesResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags:
        - User Communities
      summary: 添加用户小区
      description: 用户申请加入新的小区
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddCommunityRequest'
      responses:
        '201':
          description: 申请提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddCommunityResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/communities/{communityId}/switch:
    post:
      tags:
        - User Communities
      summary: 切换当前小区
      description: 将指定小区设置为当前活跃小区
      parameters:
        - name: communityId
          in: path
          required: true
          description: 小区ID
          schema:
            type: string
      responses:
        '200':
          description: 切换成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwitchCommunityResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /user/communities/{communityId}:
    delete:
      tags:
        - User Communities
      summary: 退出小区
      description: 用户退出指定小区
      parameters:
        - name: communityId
          in: path
          required: true
          description: 小区ID
          schema:
            type: string
      responses:
        '200':
          description: 退出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeaveCommunityResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  # ==================== Communities APIs ====================
  /communities/search:
    get:
      tags:
        - Communities
      summary: 搜索小区
      description: 根据关键词搜索小区列表
      parameters:
        - name: keyword
          in: query
          required: true
          description: 搜索关键词（小区名称或地址）
          schema:
            type: string
            minLength: 2
        - name: city
          in: query
          description: 城市筛选
          schema:
            type: string
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 50
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunitySearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /communities/{communityId}:
    get:
      tags:
        - Communities
      summary: 获取小区详情
      description: 获取指定小区的详细信息
      parameters:
        - name: communityId
          in: path
          required: true
          description: 小区ID
          schema:
            type: string
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunityDetailResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  # ==================== User Content APIs ====================
  /user/posts:
    post:
      tags:
        - User Content
      summary: 获取用户发布内容
      description: 获取用户发布的帖子列表，支持复杂查询条件、多维度筛选、排序等
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPostsSearchRequest'
      responses:
        '200':
          description: 成功获取发布内容
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPostsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /posts:
    post:
      tags:
        - User Content
      summary: 创建新发布
      description: 用户创建新的发布内容
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePostRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePostResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /posts/{postId}:
    get:
      tags:
        - User Content
      summary: 获取发布详情
      description: 获取指定发布的详细信息
      parameters:
        - name: postId
          in: path
          required: true
          description: 发布ID
          schema:
            type: string
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostDetailResponse'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - User Content
      summary: 更新发布内容
      description: 更新用户的发布内容
      parameters:
        - name: postId
          in: path
          required: true
          description: 发布ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePostRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePostResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - User Content
      summary: 删除发布
      description: 删除用户的发布内容
      parameters:
        - name: postId
          in: path
          required: true
          description: 发布ID
          schema:
            type: string
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeletePostResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /posts/{postId}/status:
    patch:
      tags:
        - User Content
      summary: 更新发布状态
      description: 更新发布的状态（上架、下架、重新发布等）
      parameters:
        - name: postId
          in: path
          required: true
          description: 发布ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePostStatusRequest'
      responses:
        '200':
          description: 状态更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePostStatusResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /posts/{postId}/statistics:
    get:
      tags:
        - User Content
      summary: 获取发布统计数据
      description: 获取发布的浏览量、点赞数、评论数等统计信息
      parameters:
        - name: postId
          in: path
          required: true
          description: 发布ID
          schema:
            type: string
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostStatisticsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /user/favorites/list:
    post:
      tags:
        - User Content
      summary: 获取用户收藏内容
      description: 获取用户收藏的内容列表，支持复杂查询条件、多维度筛选、关键词搜索等
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserFavoritesSearchRequest'
      responses:
        '200':
          description: 成功获取收藏内容
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserFavoritesResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/favorites:
    post:
      tags:
        - User Content
      summary: 添加收藏
      description: 将指定内容添加到收藏夹
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddFavoriteRequest'
      responses:
        '201':
          description: 收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddFavoriteResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/favorites/{favoriteId}:
    delete:
      tags:
        - User Content
      summary: 取消收藏
      description: 从收藏夹中移除指定内容
      parameters:
        - name: favoriteId
          in: path
          required: true
          description: 收藏记录ID
          schema:
            type: string
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveFavoriteResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /user/history:
    post:
      tags:
        - User Content
      summary: 获取用户浏览历史
      description: 获取用户的浏览历史记录，支持复杂查询条件、关键词搜索、精确时间范围等
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserHistorySearchRequest'
      responses:
        '200':
          description: 成功获取浏览历史
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserHistoryResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

    delete:
      tags:
        - User Content
      summary: 清空浏览历史
      description: 清空用户的浏览历史记录
      parameters:
        - name: type
          in: query
          description: 要清空的内容类型，不指定则清空全部
          schema:
            type: string
            enum: [all, second-hand, parking, housing, post, activity, service]
            default: all
      responses:
        '200':
          description: 清空成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClearHistoryResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/notifications:
    get:
      tags:
        - User Notifications
      summary: 获取消息通知
      description: 获取用户的消息通知列表
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
        - name: type
          in: query
          description: 通知类型
          schema:
            type: string
            enum: [all, system, like, comment, follow]
            default: all
      responses:
        '200':
          description: 成功获取通知列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotificationsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # ==================== User Settings APIs ====================
  /user/settings:
    get:
      tags:
        - User Profile
      summary: 获取用户设置
      description: 获取用户的各项设置配置
      responses:
        '200':
          description: 成功获取设置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSettingsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - User Profile
      summary: 更新用户设置
      description: 更新用户的设置配置
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserSettingsRequest'
      responses:
        '200':
          description: 设置更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/security:
    get:
      tags:
        - User Profile
      summary: 获取安全设置
      description: 获取用户的安全设置和安全评分
      responses:
        '200':
          description: 成功获取安全设置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSecurityResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - User Profile
      summary: 更新安全设置
      description: 更新用户的安全设置
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSecuritySettingsRequest'
      responses:
        '200':
          description: 安全设置更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/password:
    put:
      tags:
        - User Profile
      summary: 修改登录密码
      description: 修改用户的登录密码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200':
          description: 密码修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/phone:
    put:
      tags:
        - User Profile
      summary: 修改手机号
      description: 修改用户绑定的手机号
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePhoneRequest'
      responses:
        '200':
          description: 手机号修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/email:
    put:
      tags:
        - User Profile
      summary: 绑定/修改邮箱
      description: 绑定或修改用户邮箱
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeEmailRequest'
      responses:
        '200':
          description: 邮箱绑定/修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # ==================== User Verification APIs ====================
  /user/verification/apply:
    post:
      tags:
        - User Verification
      summary: 提交认证申请
      description: 用户提交身份认证申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerificationApplicationRequest'
      responses:
        '201':
          description: 认证申请提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationApplicationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /user/verification/documents:
    post:
      tags:
        - User Verification
      summary: 上传认证文件
      description: 上传身份认证相关文件
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 认证文件列表
                documentType:
                  type: string
                  enum: [id_card_front, id_card_back, property_certificate, rental_contract]
                  description: 文件类型
      responses:
        '200':
          description: 文件上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadDocumentsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: 未授权（Token 无效或过期）
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

  schemas:
    # ==================== Base Schemas ====================
    BaseResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
        message:
          type: string
          description: 响应消息
      required:
        - code
        - message

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
      example:
        code: 400
        message: "请求参数错误"

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
        limit:
          type: integer
          description: 每页数量
        total:
          type: integer
          description: 总记录数
        totalPages:
          type: integer
          description: 总页数

    # ==================== User Profile Schemas ====================
    UserProfile:
      type: object
      properties:
        userId:
          type: string
          description: 用户ID
          example: "888888"
        username:
          type: string
          description: 用户昵称
          example: "张三"
        avatar:
          type: string
          description: 头像URL
          example: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde"
        bio:
          type: string
          description: 个人简介
          example: "点击编辑个人资料，完善更多信息"
        phone:
          type: string
          description: 手机号
          example: "138****8888"
        email:
          type: string
          description: 邮箱
          example: "<EMAIL>"
        gender:
          type: string
          enum: [male, female, other]
          description: 性别
        birthday:
          type: string
          format: date
          description: 生日
        location:
          type: string
          description: 所在地
          example: "杭州市"
        createdAt:
          type: string
          format: date-time
          description: 注册时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    UserProfileResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserProfile'

    UpdateUserProfileRequest:
      type: object
      properties:
        username:
          type: string
          description: 用户昵称
        bio:
          type: string
          description: 个人简介
        gender:
          type: string
          enum: [male, female, other]
        birthday:
          type: string
          format: date
        location:
          type: string
          description: 所在地

    UpdateResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                updated:
                  type: boolean
                  description: 是否更新成功

    UserStatistics:
      type: object
      properties:
        postsCount:
          type: integer
          description: 发布数量
          example: 8
        favoritesCount:
          type: integer
          description: 收藏数量
          example: 12
        followingCount:
          type: integer
          description: 关注数量
          example: 25
        followersCount:
          type: integer
          description: 粉丝数量
          example: 18

    UserStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserStatistics'

    AvatarUpload:
      type: object
      properties:
        avatarUrl:
          type: string
          description: 新头像URL
          example: "https://cdn.example.com/avatars/user_888888_20240115.jpg"

    AvatarUploadResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/AvatarUpload'

    # ==================== User Verification Schemas ====================
    UserVerification:
      type: object
      properties:
        isVerified:
          type: boolean
          description: 是否已认证
        verificationType:
          type: string
          enum: [owner, tenant, property]
          description: 认证类型
        verificationStatus:
          type: string
          enum: [pending, approved, rejected]
          description: 认证状态
        verificationTime:
          type: string
          format: date-time
          description: 认证时间
        verificationDetails:
          type: object
          properties:
            realName:
              type: string
              description: 真实姓名
            idCard:
              type: string
              description: 身份证号（脱敏）
            propertyAddress:
              type: string
              description: 房产地址

    UserVerificationResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserVerification'

    # ==================== Community Schemas ====================
    Community:
      type: object
      properties:
        id:
          type: string
          description: 小区ID
          example: "comm_001"
        name:
          type: string
          description: 小区名称
          example: "春题·杭玥府"
        address:
          type: string
          description: 小区地址
          example: "杭州市拱墅区景辛路"
        city:
          type: string
          description: 所在城市
          example: "杭州市"
        district:
          type: string
          description: 所在区域
          example: "拱墅区"
        buildingCount:
          type: integer
          description: 楼栋数量
          example: 12
        unitCount:
          type: integer
          description: 单元数量
          example: 48
        householdCount:
          type: integer
          description: 户数
          example: 1200
        propertyCompany:
          type: string
          description: 物业公司
          example: "绿城物业"
        completionYear:
          type: integer
          description: 建成年份
          example: 2020
        images:
          type: array
          items:
            type: string
          description: 小区图片
        facilities:
          type: array
          items:
            type: string
          description: 小区设施
          example: ["游泳池", "健身房", "儿童乐园", "地下停车场"]

    UserCommunityInfo:
      type: object
      properties:
        id:
          type: string
          description: 小区ID
        name:
          type: string
          description: 小区名称
        address:
          type: string
          description: 小区地址
        role:
          type: string
          enum: [owner, tenant]
          description: 用户在该小区的角色
        unit:
          type: string
          description: 房屋单元
          example: "5栋1单元1302"
        isPrimary:
          type: boolean
          description: 是否为当前主要小区
        joinedAt:
          type: string
          format: date-time
          description: 加入时间
        verificationStatus:
          type: string
          enum: [pending, approved, rejected]
          description: 认证状态

    UserCommunities:
      type: object
      properties:
        currentCommunity:
          $ref: '#/components/schemas/UserCommunityInfo'
        communities:
          type: array
          items:
            $ref: '#/components/schemas/UserCommunityInfo'
        totalCount:
          type: integer
          description: 总小区数量

    UserCommunitiesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserCommunities'

    AddCommunityRequest:
      type: object
      required:
        - communityId
        - role
        - unit
      properties:
        communityId:
          type: string
          description: 小区ID
        role:
          type: string
          enum: [owner, tenant]
          description: 用户角色
        unit:
          type: string
          description: 房屋单元
          example: "5栋1单元1302"
        verificationDocuments:
          type: array
          items:
            type: string
          description: 认证文件URL列表

    AddCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                applicationId:
                  type: string
                  description: 申请ID
                status:
                  type: string
                  enum: [pending, approved, rejected]
                  description: 申请状态

    SwitchCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                currentCommunityId:
                  type: string
                  description: 当前小区ID
                switched:
                  type: boolean
                  description: 是否切换成功

    LeaveCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                left:
                  type: boolean
                  description: 是否退出成功

    CommunitySearchResult:
      type: object
      properties:
        communities:
          type: array
          items:
            $ref: '#/components/schemas/Community'
        pagination:
          $ref: '#/components/schemas/Pagination'

    CommunitySearchResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/CommunitySearchResult'

    CommunityDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Community'

    # ==================== User Content Schemas ====================
    Author:
      type: object
      properties:
        id:
          type: string
          description: 作者ID
        username:
          type: string
          description: 作者昵称
        avatar:
          type: string
          description: 作者头像

    Post:
      type: object
      properties:
        id:
          type: string
          description: 帖子ID
        type:
          type: string
          enum: [post, activity, service, second-hand, parking, housing]
          description: 帖子类型
        title:
          type: string
          description: 帖子标题
        content:
          type: string
          description: 帖子内容
        description:
          type: string
          description: 帖子描述
        images:
          type: array
          items:
            type: string
          description: 图片URL列表
        price:
          type: number
          description: 价格（适用于二手、停车位、房源等）
        priceUnit:
          type: string
          description: 价格单位
          example: "元/月"
        communityId:
          type: string
          description: 小区ID
        communityName:
          type: string
          description: 小区名称
        authorId:
          type: string
          description: 作者ID
        author:
          $ref: '#/components/schemas/Author'
        likesCount:
          type: integer
          description: 点赞数
        commentsCount:
          type: integer
          description: 评论数
        viewsCount:
          type: integer
          description: 浏览数
        favoritesCount:
          type: integer
          description: 收藏数
        status:
          type: string
          enum: [active, pending, inactive, deleted, draft]
          description: 帖子状态
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        location:
          type: string
          description: 位置信息
        contact:
          type: object
          properties:
            phone:
              type: string
              description: 联系电话
            wechat:
              type: string
              description: 微信号
            qq:
              type: string
              description: QQ号
        specifications:
          type: object
          description: 商品规格信息（适用于二手物品）
          properties:
            condition:
              type: string
              description: 成色
              example: "9成新"
            size:
              type: string
              description: 尺寸
              example: "42"
            brand:
              type: string
              description: 品牌
        propertyInfo:
          type: object
          description: 房产信息（适用于房源）
          properties:
            area:
              type: number
              description: 面积（平方米）
            rooms:
              type: string
              description: 房型
              example: "2室1厅"
            floor:
              type: string
              description: 楼层
            orientation:
              type: string
              description: 朝向
              example: "南北通透"
            furnished:
              type: boolean
              description: 是否精装修
        parkingInfo:
          type: object
          description: 停车位信息
          properties:
            location:
              type: string
              description: 位置
              example: "地下一层"
            type:
              type: string
              enum: [indoor, outdoor, underground]
              description: 停车位类型
            canShortRent:
              type: boolean
              description: 是否支持短租
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
        publishedAt:
          type: string
          format: date-time
          description: 发布时间
        expiresAt:
          type: string
          format: date-time
          description: 过期时间

    UserPosts:
      type: object
      properties:
        posts:
          type: array
          items:
            $ref: '#/components/schemas/Post'
        pagination:
          $ref: '#/components/schemas/Pagination'

    UserPostsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserPosts'

    FavoriteItem:
      type: object
      properties:
        id:
          type: string
          description: 收藏记录ID
        postId:
          type: string
          description: 帖子ID
        post:
          type: object
          properties:
            id:
              type: string
            title:
              type: string
            author:
              $ref: '#/components/schemas/Author'
            communityName:
              type: string
            createdAt:
              type: string
              format: date-time
        favoriteAt:
          type: string
          format: date-time
          description: 收藏时间

    UserFavorites:
      type: object
      properties:
        favorites:
          type: array
          items:
            $ref: '#/components/schemas/FavoriteItem'
        pagination:
          $ref: '#/components/schemas/Pagination'

    UserFavoritesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserFavorites'

    HistoryItem:
      type: object
      properties:
        id:
          type: string
          description: 历史记录ID
        postId:
          type: string
          description: 帖子ID
        post:
          type: object
          properties:
            id:
              type: string
            title:
              type: string
            author:
              $ref: '#/components/schemas/Author'
            communityName:
              type: string
        viewedAt:
          type: string
          format: date-time
          description: 浏览时间

    UserHistory:
      type: object
      properties:
        history:
          type: array
          items:
            $ref: '#/components/schemas/HistoryItem'
        pagination:
          $ref: '#/components/schemas/Pagination'

    UserHistoryResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserHistory'

    # ==================== Notification Schemas ====================
    Notification:
      type: object
      properties:
        id:
          type: string
          description: 通知ID
        type:
          type: string
          enum: [system, like, comment, follow]
          description: 通知类型
        title:
          type: string
          description: 通知标题
        content:
          type: string
          description: 通知内容
        isRead:
          type: boolean
          description: 是否已读
        relatedId:
          type: string
          description: 关联对象ID
        createdAt:
          type: string
          format: date-time
          description: 创建时间

    UserNotifications:
      type: object
      properties:
        notifications:
          type: array
          items:
            $ref: '#/components/schemas/Notification'
        unreadCount:
          type: integer
          description: 未读数量
        pagination:
          $ref: '#/components/schemas/Pagination'

    UserNotificationsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserNotifications'

    # ==================== Post Management Schemas ====================
    CreatePostRequest:
      type: object
      required:
        - type
        - title
        - content
        - communityId
      properties:
        type:
          type: string
          enum: [post, activity, service, second-hand, parking, housing]
          description: 帖子类型
        title:
          type: string
          description: 帖子标题
          maxLength: 100
        content:
          type: string
          description: 帖子内容
          maxLength: 2000
        description:
          type: string
          description: 帖子描述
          maxLength: 200
        images:
          type: array
          items:
            type: string
          description: 图片URL列表
          maxItems: 9
        price:
          type: number
          description: 价格
          minimum: 0
        priceUnit:
          type: string
          description: 价格单位
        communityId:
          type: string
          description: 小区ID
        tags:
          type: array
          items:
            type: string
          description: 标签列表
          maxItems: 5
        location:
          type: string
          description: 位置信息
        contact:
          type: object
          properties:
            phone:
              type: string
            wechat:
              type: string
            qq:
              type: string
        specifications:
          type: object
          description: 商品规格信息
        propertyInfo:
          type: object
          description: 房产信息
        parkingInfo:
          type: object
          description: 停车位信息
        expiresAt:
          type: string
          format: date-time
          description: 过期时间

    CreatePostResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                postId:
                  type: string
                  description: 新创建的帖子ID
                status:
                  type: string
                  description: 帖子状态

    UpdatePostRequest:
      type: object
      properties:
        title:
          type: string
          description: 帖子标题
          maxLength: 100
        content:
          type: string
          description: 帖子内容
          maxLength: 2000
        description:
          type: string
          description: 帖子描述
          maxLength: 200
        images:
          type: array
          items:
            type: string
          description: 图片URL列表
          maxItems: 9
        price:
          type: number
          description: 价格
          minimum: 0
        priceUnit:
          type: string
          description: 价格单位
        tags:
          type: array
          items:
            type: string
          description: 标签列表
          maxItems: 5
        location:
          type: string
          description: 位置信息
        contact:
          type: object
          properties:
            phone:
              type: string
            wechat:
              type: string
            qq:
              type: string
        specifications:
          type: object
          description: 商品规格信息
        propertyInfo:
          type: object
          description: 房产信息
        parkingInfo:
          type: object
          description: 停车位信息
        expiresAt:
          type: string
          format: date-time
          description: 过期时间

    UpdatePostResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                updated:
                  type: boolean
                  description: 是否更新成功

    UpdatePostStatusRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [active, inactive, deleted]
          description: 新状态
        reason:
          type: string
          description: 状态变更原因

    UpdatePostStatusResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                status:
                  type: string
                  description: 更新后的状态
                updated:
                  type: boolean
                  description: 是否更新成功

    DeletePostResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                deleted:
                  type: boolean
                  description: 是否删除成功

    PostDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Post'

    PostStatistics:
      type: object
      properties:
        viewsCount:
          type: integer
          description: 浏览数
        likesCount:
          type: integer
          description: 点赞数
        commentsCount:
          type: integer
          description: 评论数
        favoritesCount:
          type: integer
          description: 收藏数
        sharesCount:
          type: integer
          description: 分享数
        dailyViews:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
              views:
                type: integer
          description: 每日浏览数据
        viewSources:
          type: object
          properties:
            direct:
              type: integer
              description: 直接访问
            search:
              type: integer
              description: 搜索访问
            share:
              type: integer
              description: 分享访问
          description: 浏览来源统计

    PostStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/PostStatistics'

    # ==================== Favorites Management Schemas ====================
    AddFavoriteRequest:
      type: object
      required:
        - postId
      properties:
        postId:
          type: string
          description: 要收藏的帖子ID

    AddFavoriteResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                favoriteId:
                  type: string
                  description: 收藏记录ID
                added:
                  type: boolean
                  description: 是否添加成功

    RemoveFavoriteResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                removed:
                  type: boolean
                  description: 是否移除成功

    # ==================== History Management Schemas ====================
    ClearHistoryResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                cleared:
                  type: boolean
                  description: 是否清空成功
                clearedCount:
                  type: integer
                  description: 清空的记录数量

    # ==================== User Settings Schemas ====================
    UserSettings:
      type: object
      properties:
        notifications:
          type: object
          properties:
            pushEnabled:
              type: boolean
              description: 是否开启推送通知
            chatMessages:
              type: boolean
              description: 聊天消息通知
            systemNotifications:
              type: boolean
              description: 系统通知
            transactionReminders:
              type: boolean
              description: 交易提醒
            activityPush:
              type: boolean
              description: 活动推送
            doNotDisturb:
              type: object
              properties:
                enabled:
                  type: boolean
                  description: 是否开启勿扰模式
                startTime:
                  type: string
                  description: 开始时间
                  example: "22:00"
                endTime:
                  type: string
                  description: 结束时间
                  example: "07:00"
        privacy:
          type: object
          properties:
            profileVisibility:
              type: string
              enum: [public, friends, private]
              description: 个人资料可见性
            showOnlineStatus:
              type: boolean
              description: 显示在线状态
            allowSearchByPhone:
              type: boolean
              description: 允许通过手机号搜索
        appearance:
          type: object
          properties:
            darkMode:
              type: boolean
              description: 深色模式
            language:
              type: string
              description: 语言设置
              example: "zh-CN"
            fontSize:
              type: string
              enum: [small, medium, large]
              description: 字体大小

    UserSettingsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserSettings'

    UpdateUserSettingsRequest:
      type: object
      properties:
        notifications:
          type: object
        privacy:
          type: object
        appearance:
          type: object

    # ==================== Security Settings Schemas ====================
    UserSecurity:
      type: object
      properties:
        securityScore:
          type: integer
          description: 安全评分
          example: 90
        phoneVerified:
          type: boolean
          description: 手机号是否已验证
        emailVerified:
          type: boolean
          description: 邮箱是否已验证
        biometricEnabled:
          type: boolean
          description: 生物识别是否开启
        securityVerificationEnabled:
          type: boolean
          description: 安全验证是否开启
        lastPasswordChange:
          type: string
          format: date-time
          description: 上次密码修改时间
        loginDevices:
          type: array
          items:
            type: object
            properties:
              deviceId:
                type: string
              deviceName:
                type: string
              deviceType:
                type: string
              lastLoginTime:
                type: string
                format: date-time
              location:
                type: string
          description: 登录设备列表

    UserSecurityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserSecurity'

    UpdateSecuritySettingsRequest:
      type: object
      properties:
        biometricEnabled:
          type: boolean
        securityVerificationEnabled:
          type: boolean

    ChangePasswordRequest:
      type: object
      required:
        - oldPassword
        - newPassword
      properties:
        oldPassword:
          type: string
          description: 原密码
        newPassword:
          type: string
          description: 新密码
          minLength: 6
        confirmPassword:
          type: string
          description: 确认新密码

    ChangePhoneRequest:
      type: object
      required:
        - newPhone
        - verificationCode
      properties:
        newPhone:
          type: string
          description: 新手机号
        verificationCode:
          type: string
          description: 验证码

    ChangeEmailRequest:
      type: object
      required:
        - email
        - verificationCode
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
        verificationCode:
          type: string
          description: 验证码

    # ==================== Verification Application Schemas ====================
    VerificationApplicationRequest:
      type: object
      required:
        - verificationType
        - realName
        - idCard
        - communityId
      properties:
        verificationType:
          type: string
          enum: [owner, tenant, property]
          description: 认证类型
        realName:
          type: string
          description: 真实姓名
        idCard:
          type: string
          description: 身份证号
        communityId:
          type: string
          description: 小区ID
        propertyInfo:
          type: object
          description: 房产信息（业主认证）
          properties:
            building:
              type: string
              description: 楼栋
            unit:
              type: string
              description: 单元
            room:
              type: string
              description: 房间号
        rentalInfo:
          type: object
          description: 租住信息（租户认证）
          properties:
            building:
              type: string
              description: 楼栋
            unit:
              type: string
              description: 单元
            room:
              type: string
              description: 房间号
            rentalPeriod:
              type: object
              properties:
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
        companyInfo:
          type: object
          description: 公司信息（物业认证）
          properties:
            companyName:
              type: string
              description: 公司名称
            position:
              type: string
              description: 职位
            workId:
              type: string
              description: 工号
        documents:
          type: array
          items:
            type: string
          description: 认证文件URL列表

    VerificationApplicationResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                applicationId:
                  type: string
                  description: 申请ID
                status:
                  type: string
                  enum: [pending, approved, rejected]
                  description: 申请状态
                estimatedProcessTime:
                  type: string
                  description: 预计处理时间
                  example: "1-3个工作日"

    UploadDocumentsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                uploadedFiles:
                  type: array
                  items:
                    type: object
                    properties:
                      fileName:
                        type: string
                      fileUrl:
                        type: string
                      fileSize:
                        type: integer
                  description: 上传成功的文件列表

    # ==================== Complex Search Schemas ====================
    UserPostsSearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            page:
              type: integer
              default: 1
              minimum: 1
            limit:
              type: integer
              default: 10
              minimum: 1
              maximum: 100
        filters:
          type: object
          properties:
            types:
              type: array
              items:
                type: string
                enum: [second-hand, parking, housing, post, activity, service]
              description: 内容类型列表
            statuses:
              type: array
              items:
                type: string
                enum: [active, pending, inactive, deleted, draft]
              description: 状态列表
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  minimum: 0
                max:
                  type: number
                  minimum: 0
              description: 价格范围
            dateRange:
              type: object
              properties:
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
              description: 时间范围
            communities:
              type: array
              items:
                type: string
              description: 小区ID列表
            tags:
              type: array
              items:
                type: string
              description: 标签列表
            keyword:
              type: string
              description: 关键词搜索
              maxLength: 100
        sorting:
          type: object
          properties:
            field:
              type: string
              enum: [createdAt, updatedAt, price, viewsCount, likesCount]
              default: createdAt
              description: 排序字段
            order:
              type: string
              enum: [asc, desc]
              default: desc
              description: 排序方向

    UserFavoritesSearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            page:
              type: integer
              default: 1
              minimum: 1
            limit:
              type: integer
              default: 10
              minimum: 1
              maximum: 100
        filters:
          type: object
          properties:
            types:
              type: array
              items:
                type: string
                enum: [second-hand, parking, housing, post, activity, service]
              description: 内容类型列表
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  minimum: 0
                max:
                  type: number
                  minimum: 0
              description: 价格范围
            favoriteTimeRange:
              type: object
              properties:
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
              description: 收藏时间范围
            communities:
              type: array
              items:
                type: string
              description: 小区ID列表
            authors:
              type: array
              items:
                type: string
              description: 作者ID列表
            keyword:
              type: string
              description: 关键词搜索（标题、内容）
              maxLength: 100
        sorting:
          type: object
          properties:
            field:
              type: string
              enum: [favoriteAt, createdAt, price, viewsCount]
              default: favoriteAt
              description: 排序字段
            order:
              type: string
              enum: [asc, desc]
              default: desc
              description: 排序方向

    UserHistorySearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            page:
              type: integer
              default: 1
              minimum: 1
            limit:
              type: integer
              default: 10
              minimum: 1
              maximum: 100
        filters:
          type: object
          properties:
            types:
              type: array
              items:
                type: string
                enum: [second-hand, parking, housing, post, activity, service]
              description: 内容类型列表
            viewTimeRange:
              type: object
              properties:
                startDate:
                  type: string
                  format: date-time
                endDate:
                  type: string
                  format: date-time
              description: 浏览时间范围
            communities:
              type: array
              items:
                type: string
              description: 小区ID列表
            authors:
              type: array
              items:
                type: string
              description: 作者ID列表
            keyword:
              type: string
              description: 关键词搜索（标题、内容）
              maxLength: 100
            excludeTypes:
              type: array
              items:
                type: string
              description: 排除的内容类型
        sorting:
          type: object
          properties:
            field:
              type: string
              enum: [viewedAt, createdAt, viewsCount]
              default: viewedAt
              description: 排序字段
            order:
              type: string
              enum: [asc, desc]
              default: desc
              description: 排序方向
        grouping:
          type: object
          properties:
            enabled:
              type: boolean
              default: false
              description: 是否启用分组
            groupBy:
              type: string
              enum: [date, type, community, author]
              description: 分组方式
