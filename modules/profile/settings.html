<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 12px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }

        /* iOS列表样式 */
        .ios-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .ios-list-header {
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            text-transform: uppercase;
            color: var(--ios-secondaryLabel);
            letter-spacing: 0.01em;
            margin-bottom: 0;
        }
        
        .ios-list-item {
            padding: 11px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--ios-systemBackground);
            transition: background-color 0.2s;
            border-bottom: 0.5px solid rgba(60,60,67,0.08);
        }
        
        .ios-list-item:last-child {
            border-bottom: none;
        }
        
        .ios-list-item:active {
            background-color: rgba(60,60,67,0.05);
        }
        
        .ios-list-item-content {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .ios-list-item-title {
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .ios-list-item-subtitle {
            font-size: 14px;
            color: var(--ios-secondaryLabel);
            margin-right: 5px;
        }
        
        .ios-list-item-accessory {
            color: var(--ios-secondaryLabel);
            margin-left: 12px;
        }
        
        /* iOS开关组件 */
        .ios-switch {
            position: relative;
            display: inline-block;
            width: 51px;
            height: 31px;
            flex-shrink: 0;
        }
        
        .ios-switch input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }
        
        .ios-switch-track {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--ios-light-gray);
            transition: .4s;
            border-radius: 16px;
        }
        
        .ios-switch-track:before {
            position: absolute;
            content: "";
            height: 28px;
            width: 28px;
            left: 2px;
            bottom: 1.5px;
            background-color: white;
            transition: .25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-radius: 50%;
            box-shadow: 0 1px 4px rgba(0,0,0,0.15);
        }
        
        .ios-switch input:checked + .ios-switch-track {
            background-color: var(--ios-green);
        }
        
        .ios-switch input:checked + .ios-switch-track:before {
            transform: translateX(19px);
        }
        
        /* iOS退出按钮 */
        .ios-sign-out-btn {
            background-color: var(--ios-systemBackground);
            color: var(--ios-red);
            padding: 14px 0;
            width: 100%;
            border-radius: 12px;
            text-align: center;
            font-size: 17px;
            font-weight: 600;
            transition: background-color 0.2s ease;
        }
        
        .ios-sign-out-btn:active {
            background-color: rgba(255, 59, 48, 0.1);
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen ios-scroll-indicator smooth-scroll">
            <div class="screen-title">设置</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">设置</h1>
                <div></div>
            </div>
            
            <div class="px-4 py-4 pb-32">
                <!-- 账号设置 -->
                <div class="ios-fade-in">
                    <div class="ios-list-header mb-1">账号设置</div>
                    <div class="ios-card">
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='profile.html'">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#007AFF] mr-3">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span class="ios-list-item-title">个人资料</span>
                            </div>
                            <div class="ios-list-item-accessory">
                                <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                            </div>
                        </div>
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='security.html'">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#34C759] mr-3">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <span class="ios-list-item-title">账号安全</span>
                            </div>
                            <div class="ios-list-item-accessory">
                                <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通用设置 -->
                <div class="ios-fade-in" style="animation-delay: 0.1s;">
                    <div class="ios-list-header mb-1 mt-4">通用设置</div>
                    <div class="ios-card">
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='notifications.html'">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#FF9500] mr-3">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <span class="ios-list-item-title">消息通知</span>
                            </div>
                            <div class="ios-list-item-accessory">
                                <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                            </div>
                        </div>
                        <div class="ios-list-item">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#5856D6] mr-3">
                                    <i class="fas fa-moon"></i>
                                </div>
                                <span class="ios-list-item-title">深色模式</span>
                            </div>
                            <label class="ios-switch">
                                <input type="checkbox" id="darkModeToggle">
                                <span class="ios-switch-track"></span>
                            </label>
                        </div>
                        <div class="ios-list-item ios-haptic">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#5E5CE6] mr-3">
                                    <i class="fas fa-language"></i>
                                </div>
                                <span class="ios-list-item-title">语言</span>
                            </div>
                            <div class="flex items-center">
                                <span class="ios-list-item-subtitle">简体中文</span>
                                <div class="ios-list-item-accessory">
                                    <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 隐私设置 -->
                <div class="ios-fade-in" style="animation-delay: 0.15s;">
                    <div class="ios-list-header mb-1 mt-4">隐私设置</div>
                    <div class="ios-card">
                        <div class="ios-list-item ios-haptic">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#FF3B30] mr-3">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <span class="ios-list-item-title">隐私政策</span>
                            </div>
                            <div class="ios-list-item-accessory">
                                <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                            </div>
                        </div>
                        <div class="ios-list-item ios-haptic">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#FF9500] mr-3">
                                    <i class="fas fa-cookie"></i>
                                </div>
                                <span class="ios-list-item-title">Cookie设置</span>
                            </div>
                            <div class="ios-list-item-accessory">
                                <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他 -->
                <div class="ios-fade-in" style="animation-delay: 0.2s;">
                    <div class="ios-list-header mb-1 mt-4">其他</div>
                    <div class="ios-card">
                        <div class="ios-list-item ios-haptic">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#007AFF] mr-3">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <span class="ios-list-item-title">关于我们</span>
                            </div>
                            <div class="ios-list-item-accessory">
                                <i class="fas fa-chevron-right text-[var(--ios-secondaryLabel)]"></i>
                            </div>
                        </div>
                        <div class="ios-list-item ios-haptic" id="clearCacheBtn">
                            <div class="ios-list-item-content">
                                <div class="w-8 h-8 flex items-center justify-center text-[#FF3B30] mr-3">
                                    <i class="fas fa-trash"></i>
                                </div>
                                <span class="ios-list-item-title">清除缓存</span>
                            </div>
                            <span class="ios-list-item-subtitle">2.1MB</span>
                        </div>
                    </div>
                </div>

                <!-- 退出登录按钮 -->
                <div class="mt-6 ios-fade-in" style="animation-delay: 0.25s;">
                    <button class="ios-sign-out-btn ios-button ios-haptic">
                        退出登录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 清除缓存按钮的点击事件
            document.getElementById('clearCacheBtn').addEventListener('click', function() {
                // 显示iOS样式的确认对话框
                if (confirm('确定要清除所有缓存吗？')) {
                    alert('缓存已清除');
                    this.querySelector('.ios-list-item-subtitle').textContent = '0 MB';
                }
            });
            
            // 深色模式切换
            const darkModeToggle = document.getElementById('darkModeToggle');
            darkModeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-mode');
                    // 这里可以添加实际的深色模式切换逻辑
                } else {
                    document.body.classList.remove('dark-mode');
                }
            });
        });
    </script>
</body>
</html> 