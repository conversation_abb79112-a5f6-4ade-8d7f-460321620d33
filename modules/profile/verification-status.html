<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 认证状态</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS固定按钮 */
        .ios-fixed-btn {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 12px;
            font-weight: 500;
            font-size: 16px;
            padding: 14px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.2s ease;
        }
        
        .ios-fixed-btn:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,122,255,0.2);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }

        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* iOS标签样式 */
        .ios-badge {
            display: inline-flex;
            align-items: center;
            padding: 5px 12px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.15s ease;
        }
        
        /* iOS进度线 */
        .ios-progress-line {
            height: 2px;
            background: var(--ios-light-gray);
            position: relative;
            margin: 32px 0;
        }
        
        .ios-progress-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: var(--ios-blue);
            border-radius: 1px;
        }
        
        .ios-progress-step {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 26px;
            height: 26px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }
        
        .ios-progress-step.active {
            background-color: var(--ios-blue);
        }
        
        .ios-progress-step.inactive {
            background-color: var(--ios-light-gray);
        }
        
        .ios-progress-label {
            position: absolute;
            top: calc(100% + 8px);
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* iOS列表项样式 */
        .ios-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 14px 16px;
            border-bottom: 0.5px solid rgba(60,60,67,0.1);
        }
        
        .ios-list-item:last-child {
            border-bottom: none;
        }
        
        .ios-list-label {
            font-size: 15px;
            color: var(--ios-secondaryLabel);
        }
        
        .ios-list-value {
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-label);
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">认证状态</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">认证状态</h1>
                <div></div>
            </div>

            <div class="px-4 pb-32">
                <!-- 认证状态内容 -->
                <div class="mt-4 ios-fade-in">
                    <!-- 成功状态 -->
                    <div id="status-success" class="ios-card">
                        <!-- 状态头部 -->
                        <div class="bg-[#34C759] p-6 text-center text-white">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                                <i class="fas fa-check text-3xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">认证成功</h2>
                            <p class="mt-2 text-sm opacity-90">恭喜您，您的业主认证已通过</p>
                        </div>
                        
                        <!-- 认证信息 -->
                        <div class="p-0">
                            <div class="ios-list-item">
                                <div class="ios-list-label">认证类型</div>
                                <div class="ios-list-value">业主认证</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">认证时间</div>
                                <div class="ios-list-value">2023-07-15 14:30</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">认证小区</div>
                                <div class="ios-list-value">春题·杭玥府</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">住址信息</div>
                                <div class="ios-list-value">5栋1单元1302</div>
                            </div>
                        </div>
                            
                        <!-- 认证徽章 -->
                        <div class="p-4 border-t border-[rgba(60,60,67,0.1)]">
                            <h3 class="text-base font-medium mb-3">您已获得以下徽章</h3>
                            <div class="flex items-center space-x-3">
                                <div class="ios-badge bg-[rgba(255,149,0,0.1)] border border-[rgba(255,149,0,0.2)] text-[#FF9500]">
                                    <i class="fas fa-home mr-2"></i>
                                    <span>认证业主</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审核中状态 -->
                    <div id="status-pending" class="ios-card hidden">
                        <!-- 状态头部 -->
                        <div class="bg-[#007AFF] p-6 text-center text-white">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                                <i class="fas fa-clock text-3xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">审核中</h2>
                            <p class="mt-2 text-sm opacity-90">您的认证申请正在审核，请耐心等待</p>
                        </div>
                        
                        <!-- 认证信息 -->
                        <div class="p-0">
                            <div class="ios-list-item">
                                <div class="ios-list-label">认证类型</div>
                                <div class="ios-list-value">业主认证</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">申请时间</div>
                                <div class="ios-list-value">2023-07-15 10:30</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">预计完成</div>
                                <div class="ios-list-value">1-3个工作日</div>
                            </div>
                        </div>
                            
                        <!-- 审核进度 -->
                        <div class="p-4 border-t border-[rgba(60,60,67,0.1)]">
                            <h3 class="text-base font-medium mb-3">审核进度</h3>
                            <div class="ios-progress-line relative mx-8 mt-8" style="height:2px;">
                                <div class="ios-progress-step active" style="left: 0%;">
                                    <span>1</span>
                                    <span class="ios-progress-label text-[#007AFF]">提交申请</span>
                                </div>
                                <div class="ios-progress-step active" style="left: 50%;">
                                    <span>2</span>
                                    <span class="ios-progress-label text-[#007AFF]">资料审核</span>
                                </div>
                                <div class="ios-progress-step inactive" style="left: 100%;">
                                    <span>3</span>
                                    <span class="ios-progress-label text-[var(--ios-secondaryLabel)]">认证完成</span>
                                </div>
                                <div style="width:50%;" class="absolute top-0 left-0 h-full bg-[#007AFF]"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 失败状态 -->
                    <div id="status-failed" class="ios-card hidden">
                        <!-- 状态头部 -->
                        <div class="bg-[#FF3B30] p-6 text-center text-white">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                                <i class="fas fa-times text-3xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">认证失败</h2>
                            <p class="mt-2 text-sm opacity-90">很抱歉，您的认证申请未通过审核</p>
                        </div>
                        
                        <!-- 认证信息 -->
                        <div class="p-0">
                            <div class="ios-list-item">
                                <div class="ios-list-label">认证类型</div>
                                <div class="ios-list-value">业主认证</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">申请时间</div>
                                <div class="ios-list-value">2023-07-15 10:30</div>
                            </div>
                            <div class="ios-list-item">
                                <div class="ios-list-label">审核结果</div>
                                <div class="ios-list-value text-[#FF3B30]">未通过</div>
                            </div>
                        </div>
                            
                        <!-- 失败原因 -->
                        <div class="p-4 border-t border-[rgba(60,60,67,0.1)]">
                            <div class="bg-[rgba(255,59,48,0.08)] p-4 rounded-xl mb-4">
                                <h3 class="font-medium mb-2 flex items-center">
                                    <i class="fas fa-info-circle text-[#FF3B30] mr-2"></i>
                                    <span>未通过原因</span>
                                </h3>
                                <p class="text-sm text-[var(--ios-secondaryLabel)]">提交的房产证信息与实际不符，请确认后重新提交正确的认证材料。</p>
                            </div>
                            
                            <!-- 重新认证按钮 -->
                            <button class="ios-fixed-btn ios-button ios-haptic bg-[#FF3B30]" style="box-shadow: 0 2px 8px rgba(255,59,48,0.3);" onclick="window.location.href='user-verification.html'">
                                重新认证
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 快速查看不同状态的链接 -->
                <div class="ios-card p-4 mt-6 ios-fade-in" style="animation-delay: 0.1s;">
                    <h3 class="text-base font-medium mb-3 text-center">快速查看不同认证状态（仅演示用）</h3>
                    <div class="flex justify-around gap-3">
                        <a href="verification-status.html" class="bg-[#34C759] text-white py-2 px-4 rounded-xl text-sm flex-1 text-center ios-button ios-haptic">认证成功</a>
                        <a href="verification-status.html?status=pending" class="bg-[#007AFF] text-white py-2 px-4 rounded-xl text-sm flex-1 text-center ios-button ios-haptic">审核中</a>
                        <a href="verification-status.html?status=failed" class="bg-[#FF3B30] text-white py-2 px-4 rounded-xl text-sm flex-1 text-center ios-button ios-haptic">审核失败</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 可以通过URL参数或其他方式切换不同状态的显示
            // 例如：?status=pending 或 ?status=failed
            
            const urlParams = new URLSearchParams(window.location.search);
            const status = urlParams.get('status');
            
            const successEl = document.getElementById('status-success');
            const pendingEl = document.getElementById('status-pending');
            const failedEl = document.getElementById('status-failed');
            
            // 默认显示成功状态
            if (status === 'pending') {
                successEl.classList.add('hidden');
                pendingEl.classList.remove('hidden');
                failedEl.classList.add('hidden');
            } else if (status === 'failed') {
                successEl.classList.add('hidden');
                pendingEl.classList.add('hidden');
                failedEl.classList.remove('hidden');
            } else {
                successEl.classList.remove('hidden');
                pendingEl.classList.add('hidden');
                failedEl.classList.add('hidden');
            }
        });
    </script>
</body>
</html> 