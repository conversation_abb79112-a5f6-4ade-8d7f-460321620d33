<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        /* 深色模式支持 - 已注释掉，避免系统默认使用深色模式 */
        @media (prefers-color-scheme: dark) {
            body.dark-theme {
                --ios-systemBackground: #000000;
                --ios-secondarySystemBackground: #1C1C1E;
                --ios-tertiarySystemBackground: #2C2C2E;
                --ios-groupedBackground: #000000;
                --ios-card: #1C1C1E;
                
                --ios-label: #FFFFFF;
                --ios-secondaryLabel: #EBEBF599;
                --ios-tertiaryLabel: #EBEBF54D;
                --ios-quaternaryLabel: #EBEBF52E;
            }
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS搜索框样式 */
        .ios-search {
            border-radius: 10px;
            background-color: var(--ios-light-gray);
            height: 36px;
            transition: background-color 0.2s ease;
        }
        
        .ios-search:focus-within {
            background-color: #E0E0E6;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS隔离视图风格 */
        .ios-grouped-section {
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
        }
        
        /* iOS列表分割线样式 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin-left: 16px;
        }

        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }

        /* 搜索栏容器 */
        .ios-search-container {
            position: sticky;
            top: 44px;
            z-index: 20;
            background-color: var(--ios-secondarySystemBackground);
            padding: 12px 16px 8px;
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
        }
        
        /* iOS动画效果 */
        @keyframes ios-ripple {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        /* SF符号效果 */
        .sf-symbol {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 14px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        .prototype-screen {
            background-color: var(--ios-secondarySystemBackground);
        }

        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 水波纹效果 */
        .ios-ripple-effect {
            position: relative;
            overflow: hidden;
        }

        .ios-ripple-effect::after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.4s, opacity 0.8s;
        }

        .ios-ripple-effect:active::after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-top: 4px;
            padding-bottom: 100px;
        }
        
        /* iOS小区切换按钮 */
        .ios-location-button {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background-color: rgba(0,122,255,0.1);
            border-radius: 16px;
            color: var(--ios-blue);
            font-weight: 500;
            font-size: 13px;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">首页</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            <!-- 搜索栏 -->
            <div class="ios-search-container">
                <div class="flex items-center ios-search px-3">
                    <i class="fas fa-search text-[#8E8E93] text-xs"></i>
                    <input type="text" placeholder="搜索" class="ml-2 bg-transparent flex-1 outline-none text-sm h-full">
                </div>
            </div>

            <div class="ios-content-area">
                <!-- 当前小区信息栏 -->
                <div class="px-4 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mr-2">
                            <i class="fas fa-map-marker-alt text-[#007AFF] text-xs"></i>
                        </div>
                        <div>
                            <div class="text-[15px] font-medium">春题·杭玥府</div>
                            <div class="text-xs text-[#8E8E93]">5栋1单元1302</div>
                        </div>
                    </div>
                    <button onclick="window.location.href='my-communities.html'" class="ios-location-button ios-button">
                        <span>切换小区</span>
                        <i class="fas fa-chevron-down ml-1 text-[10px]"></i>
                    </button>
                </div>

                <!-- 功能入口 -->
                <div class="ios-card mx-4 my-4 py-6 px-3 ios-fade-in">
                    <div class="grid grid-cols-4 gap-1">
                        <button onclick="window.location.href='../second-hand/second-hand-market.html'"  class="flex flex-col items-center ios-button ios-haptic px-1">
                            <div class="w-[52px] h-[52px] rounded-[14px] flex items-center justify-center mb-1.5" style="background: linear-gradient(145deg, rgba(255,45,85,0.12), rgba(255,59,48,0.12))">
                                <i class="fas fa-shopping-bag text-[#FF2D55] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium">二手闲置</span>
                        </button>
                        <button onclick="window.location.href='../house/house-market.html'" class="flex flex-col items-center ios-button ios-haptic px-1">
                            <div class="w-[52px] h-[52px] rounded-[14px] flex items-center justify-center mb-1.5" style="background: linear-gradient(145deg, rgba(88,86,214,0.12), rgba(94,92,230,0.12))">
                                <i class="fas fa-home text-[#5856D6] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium">房屋租赁</span>
                        </button>
                        <button onclick="window.location.href='../parking/parking-market.html'"  class="flex flex-col items-center ios-button ios-haptic px-1">
                            <div class="w-[52px] h-[52px] rounded-[14px] flex items-center justify-center mb-1.5" style="background: linear-gradient(145deg, rgba(0,122,255,0.12), rgba(90,200,250,0.12))">
                                <i class="fas fa-parking text-[#007AFF] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium">停车位</span>
                        </button>
                        <button onclick="window.location.href='../market/market-home.html'"  class="flex flex-col items-center ios-button ios-haptic px-1">
                            <div class="w-[52px] h-[52px] rounded-[14px] flex items-center justify-center mb-1.5" style="background: linear-gradient(145deg, rgba(52,199,89,0.12), rgba(48,209,88,0.12))">
                                <i class="fas fa-shopping-basket text-[#34C759] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium">邻里集市</span>
                        </button>
                    </div>
                </div>

                <!-- 二手闲置板块 -->
                <div class="px-4 mb-6 ios-fade-in" style="animation-delay: 0.1s;">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="ios-section-title">二手闲置</h2>
                        <a href="second-hand-market.html" class="text-sm text-[#007AFF] flex items-center ios-button">
                            查看全部
                            <i class="fas fa-chevron-right ml-1 text-[10px]"></i>
                        </a>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="ios-card overflow-hidden ios-haptic ios-ripple-effect">
                            <div class="aspect-square relative">
                                <img src="https://images.unsplash.com/photo-1678911820864-e2c567c655d7" class="w-full h-full object-cover absolute inset-0">
                            </div>
                            <div class="p-2.5">
                                <h3 class="text-[14px] font-medium leading-tight truncate">iPhone 14 Pro Max</h3>
                                <p class="text-[12px] text-[#8E8E93] mt-0.5">95新</p>
                                <p class="ios-price text-[15px] mt-1">¥7,999</p>
                            </div>
                        </div>
                        <div class="ios-card overflow-hidden ios-haptic ios-ripple-effect">
                            <div class="aspect-square relative">
                                <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" class="w-full h-full object-cover absolute inset-0">
                            </div>
                            <div class="p-2.5">
                                <h3 class="text-[14px] font-medium leading-tight truncate">Nike Air Max</h3>
                                <p class="text-[12px] text-[#8E8E93] mt-0.5">全新</p>
                                <p class="ios-price text-[15px] mt-1">¥599</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 停车位租售板块 -->
                <div class="px-4 mb-6 ios-fade-in" style="animation-delay: 0.15s;">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="ios-section-title">停车位租售</h2>
                        <a href="parking-market.html" class="text-sm text-[#007AFF] flex items-center ios-button">
                            查看全部
                            <i class="fas fa-chevron-right ml-1 text-[10px]"></i>
                        </a>
                    </div>
                    <div class="space-y-3">
                        <div class="ios-card p-3 flex space-x-3 ios-button ios-haptic ios-ripple-effect">
                            <div class="w-[72px] h-[72px] rounded-lg overflow-hidden flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 flex flex-col justify-between py-0.5">
                                <div>
                                    <h3 class="text-[15px] font-medium">阳光花园停车位</h3>
                                    <p class="text-[12px] text-[#8E8E93] mt-0.5">24小时保安 可短租</p>
                                </div>
                                <p class="ios-price text-[15px]">¥300/月</p>
                            </div>
                            <i class="fas fa-angle-right text-[#C7C7CC] self-center"></i>
                        </div>
                        <div class="ios-card p-3 flex space-x-3 ios-button ios-haptic ios-ripple-effect">
                            <div class="w-[72px] h-[72px] rounded-lg overflow-hidden flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 flex flex-col justify-between py-0.5">
                                <div>
                                    <h3 class="text-[15px] font-medium">幸福小区车位</h3>
                                    <p class="text-[12px] text-[#8E8E93] mt-0.5">临近地铁 有充电桩</p>
                                </div>
                                <p class="ios-price text-[15px]">¥350/月</p>
                            </div>
                            <i class="fas fa-angle-right text-[#C7C7CC] self-center"></i>
                        </div>
                    </div>
                </div>

                <!-- 房源租赁板块 -->
                <div class="px-4 mb-6 ios-fade-in" style="animation-delay: 0.2s;">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="ios-section-title">房源租赁</h2>
                        <a href="house-market.html" class="text-sm text-[#007AFF] flex items-center ios-button">
                            查看全部
                            <i class="fas fa-chevron-right ml-1 text-[10px]"></i>
                        </a>
                    </div>
                    <div class="ios-card overflow-hidden ios-button ios-haptic ios-ripple-effect">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1502672260266-1c1ef2d93688" class="w-full h-40 object-cover">
                            <div class="absolute top-2 right-2 bg-black bg-opacity-50 backdrop-blur-md text-white text-xs px-2 py-1 rounded-full flex items-center">
                                <i class="fas fa-image mr-1 text-[10px]"></i><span>12张</span>
                            </div>
                        </div>
                        <div class="p-3.5">
                            <div class="flex items-start justify-between">
                                <h3 class="text-[17px] font-medium leading-tight">阳光花园 2室1厅</h3>
                                <p class="ios-price text-[17px]">¥3,500</p>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] mt-1">精装修 家电齐全 拎包入住</p>
                            <div class="flex flex-wrap items-center gap-2 mt-2">
                                <span class="ios-tag">整租</span>
                                <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">随时看房</span>
                                <span class="text-xs text-[#8E8E93]">85m²</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近浏览板块 -->
                <div class="px-4 mb-6 ios-fade-in" style="animation-delay: 0.25s;">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="ios-section-title">最近浏览</h2>
                        <a href="#" class="text-sm text-[#007AFF] flex items-center ios-button">
                            更多
                            <i class="fas fa-chevron-right ml-1 text-[10px]"></i>
                        </a>
                    </div>
                    <div class="ios-card">
                        <div class="flex items-center space-x-3 p-3.5 ios-button ios-haptic ios-ripple-effect">
                            <div class="w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1">
                                <h3 class="text-[15px] font-medium">幸福小区地下车位</h3>
                                <p class="text-xs text-[#8E8E93]">2小时前</p>
                            </div>
                            <i class="fas fa-angle-right text-[#C7C7CC]"></i>
                        </div>
                        <div class="ios-separator"></div>
                        <div class="flex items-center space-x-3 p-3.5 ios-button ios-haptic ios-ripple-effect">
                            <div class="w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1">
                                <h3 class="text-[15px] font-medium">Nike Air Max</h3>
                                <p class="text-xs text-[#8E8E93]">昨天</p>
                            </div>
                            <i class="fas fa-angle-right text-[#C7C7CC]"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-1.5 pb-6 z-40">
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home text-[#007AFF] text-[20px]"></i>
                    <span class="text-[10px] text-[#007AFF] mt-0.5 font-medium">首页</span>
                </button>
                <button onclick="window.location.href='discover.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">发现</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="window.location.href='messages.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">消息</span>
                </button>
                <button onclick="window.location.href='../profile/profile.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 