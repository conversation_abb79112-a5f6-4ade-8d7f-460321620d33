<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 发现</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        /* 深色模式支持 - 已注释掉，避免系统默认使用深色模式 */
        @media (prefers-color-scheme: dark) {
            body.dark-theme {
                --ios-systemBackground: #000000;
                --ios-secondarySystemBackground: #1C1C1E;
                --ios-tertiarySystemBackground: #2C2C2E;
                --ios-groupedBackground: #000000;
                --ios-card: #1C1C1E;
                
                --ios-label: #FFFFFF;
                --ios-secondaryLabel: #EBEBF599;
                --ios-tertiaryLabel: #EBEBF54D;
                --ios-quaternaryLabel: #EBEBF52E;
            }
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS搜索框样式 */
        .ios-search {
            border-radius: 10px;
            background-color: var(--ios-light-gray);
            height: 36px;
            transition: background-color 0.2s ease;
        }
        
        .ios-search:focus-within {
            background-color: #E0E0E6;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS隔离视图风格 */
        .ios-grouped-section {
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
        }
        
        /* iOS列表分割线样式 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin-left: 16px;
        }

        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }

        /* 搜索栏容器 */
        .ios-search-container {
            position: sticky;
            top: 44px;
            z-index: 20;
            background-color: var(--ios-systemBackground);
            padding: 8px 16px;
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
        }
        
        /* iOS动画效果 */
        @keyframes ios-ripple {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        /* SF符号效果 */
        .sf-symbol {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 14px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        .prototype-screen {
            background-color: var(--ios-secondarySystemBackground);
        }

        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 水波纹效果 */
        .ios-ripple-effect {
            position: relative;
            overflow: hidden;
        }

        .ios-ripple-effect::after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.4s, opacity 0.8s;
        }

        .ios-ripple-effect:active::after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-top: 4px;
            padding-bottom: 100px;
        }
        
        /* 分类标签栏 */
        .ios-category-bar {
            position: sticky;
            top: 92px;
            z-index: 15;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
            padding: 8px 16px;
        }
        
        /* 分类标签按钮 */
        .ios-category-button {
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
            background-color: var(--ios-light-gray);
            color: var(--ios-secondaryLabel);
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .ios-category-button.active {
            background-color: var(--ios-blue);
            color: white;
        }
        
        /* 社交互动按钮 */
        .ios-social-button {
            display: flex;
            align-items: center;
            color: var(--ios-gray);
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 20px;
            transition: background-color 0.2s ease;
        }
        
        .ios-social-button:active {
            background-color: rgba(0,0,0,0.05);
        }
        
        /* 帖子卡片样式 */
        .ios-post-card {
            background-color: white;
            margin-bottom: 8px;
            padding: 16px;
        }
        
        /* 帖子作者信息区 */
        .post-author-info {
            margin-bottom: 10px;
        }
        
        /* 帖子内容区 */
        .post-content {
            margin-bottom: 12px;
            letter-spacing: -0.01em;
            line-height: 1.4;
        }
        
        /* 帖子多图区 */
        .post-images {
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 12px;
        }
        
        /* 帖子操作区 */
        .post-actions {
            padding-top: 6px;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">发现</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 搜索栏 -->
            <div class="ios-search-container">
                <div class="flex items-center ios-search px-3">
                    <i class="fas fa-search text-[#8E8E93] text-xs"></i>
                    <input type="text" placeholder="搜索" class="ml-2 bg-transparent flex-1 outline-none text-sm h-full">
                </div>
            </div>

            <!-- 分类标签 -->
            <div class="ios-category-bar">
                <div class="flex space-x-3 overflow-x-auto ios-scroll-indicator">
                    <button class="ios-category-button active ios-button ios-haptic">全部</button>
                    <button class="ios-category-button ios-button ios-haptic">二手闲置</button>
                    <button class="ios-category-button ios-button ios-haptic">房源租赁</button>
                    <button class="ios-category-button ios-button ios-haptic">车位出租</button>
                    <button class="ios-category-button ios-button ios-haptic">最新发布</button>
                    <button class="ios-category-button ios-button ios-haptic">热门推荐</button>
                </div>
            </div>

            <div class="ios-content-area">
                <!-- 内容列表 -->
                <div>
                    <!-- 列表项1 -->
                    <div class="ios-post-card ios-fade-in" style="animation-delay: 0.05s;">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="w-10 h-10 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="post-author-info flex justify-between items-center">
                                    <h3 class="text-[15px] font-semibold">张三</h3>
                                    <span class="text-xs text-[#8E8E93]">5分钟前</span>
                                </div>
                                <p class="post-content text-[15px]">出售全新 iPhone 14 Pro Max，原价9999，现价7999，无拆无修，整机保修10个月。</p>
                                <div class="post-images">
                                    <img src="https://images.unsplash.com/photo-1678911820864-e2c567c655d7" class="w-full aspect-square object-cover rounded-lg">
                                </div>
                                <div class="post-actions flex justify-between items-center">
                                    <div class="flex items-center">
                                        <button class="ios-social-button ios-button ios-haptic mr-1">
                                            <i class="far fa-heart mr-1.5 text-[15px]"></i>
                                            <span>12</span>
                                        </button>
                                        <button class="ios-social-button ios-button ios-haptic mr-2">
                                            <i class="far fa-comment mr-1.5 text-[15px]"></i>
                                            <span>8</span>
                                        </button>
                                        <span class="ios-tag">二手闲置</span>
                                    </div>
                                    <span class="ios-price text-[16px]">¥7,999</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 列表项2 -->
                    <div class="ios-post-card ios-fade-in" style="animation-delay: 0.1s;">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="w-10 h-10 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="post-author-info flex justify-between items-center">
                                    <h3 class="text-[15px] font-semibold">张三</h3>
                                    <span class="text-xs text-[#8E8E93]">20分钟前</span>
                                </div>
                                <p class="post-content text-[15px]">阳光花园小区地下停车位出租，24小时保安，月租300元，支持短租。</p>
                                <div class="post-images grid grid-cols-2 gap-2">
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full aspect-square object-cover">
                                    </div>
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full aspect-square object-cover">
                                    </div>
                                </div>
                                <div class="post-actions flex justify-between items-center">
                                    <div class="flex items-center">
                                        <button class="ios-social-button ios-button ios-haptic mr-1">
                                            <i class="far fa-heart mr-1.5 text-[15px]"></i>
                                            <span>6</span>
                                        </button>
                                        <button class="ios-social-button ios-button ios-haptic mr-2">
                                            <i class="far fa-comment mr-1.5 text-[15px]"></i>
                                            <span>3</span>
                                        </button>
                                        <span class="ios-tag" style="background-color: rgba(0,122,255,0.1); color: var(--ios-blue);">停车位出租</span>
                                    </div>
                                    <span class="ios-price text-[16px]">¥300/月</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 列表项3 -->
                    <div class="ios-post-card ios-fade-in" style="animation-delay: 0.15s;">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36" class="w-10 h-10 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="post-author-info flex justify-between items-center">
                                    <h3 class="text-[15px] font-semibold">李四</h3>
                                    <span class="text-xs text-[#8E8E93]">1小时前</span>
                                </div>
                                <p class="post-content text-[15px]">阳光花园小区2室1厅出租，精装修，家电齐全，拎包入住，交通便利。</p>
                                <div class="post-images grid grid-cols-3 gap-1.5">
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="https://images.unsplash.com/photo-1502672260266-1c1ef2d93688" class="w-full aspect-square object-cover">
                                    </div>
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="https://images.unsplash.com/photo-1502005229762-cf1b2da7c5d6" class="w-full aspect-square object-cover">
                                    </div>
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="https://images.unsplash.com/photo-1502005097973-6a7082348e28" class="w-full aspect-square object-cover">
                                    </div>
                                </div>
                                <div class="post-actions flex justify-between items-center">
                                    <div class="flex items-center">
                                        <button class="ios-social-button ios-button ios-haptic mr-1">
                                            <i class="far fa-heart mr-1.5 text-[15px]"></i>
                                            <span>15</span>
                                        </button>
                                        <button class="ios-social-button ios-button ios-haptic mr-2">
                                            <i class="far fa-comment mr-1.5 text-[15px]"></i>
                                            <span>10</span>
                                        </button>
                                        <span class="ios-tag" style="background-color: rgba(88,86,214,0.1); color: var(--ios-purple);">房源租赁</span>
                                    </div>
                                    <span class="ios-price text-[16px]">¥3,500/月</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-1.5 pb-6 z-40">
                <button onclick="window.location.href='home.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">首页</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass text-[#007AFF] text-[22px]"></i>
                    <span class="text-[10px] text-[#007AFF] mt-0.5 font-medium">发现</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="window.location.href='messages.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">消息</span>
                </button>
                <button onclick="window.location.href='../profile/profile.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 分类标签切换
            const categoryButtons = document.querySelectorAll('.ios-category-button');
            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    categoryButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 