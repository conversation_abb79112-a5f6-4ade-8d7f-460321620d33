<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 停车位详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow - 更微妙的阴影 */
            box-shadow: 0 2px 6px rgba(0,0,0,0.02), 0 1px 3px rgba(0,0,0,0.03);
            transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform;
        }
        
        .ios-card:active {
            transform: scale(0.97);
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
            background-color: rgba(0,0,0,0.01);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 17px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 11px;
            font-weight: 500;
            letter-spacing: -0.01em;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
            letter-spacing: -0.01em;
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(12px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.5s cubic-bezier(0.24, 0.22, 0.015, 1.0) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-bottom: 110px;
        }
        
        /* iOS固定按钮 */
        .ios-fixed-btn {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 12px;
            font-weight: 500;
            font-size: 16px;
            padding: 14px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.2s ease;
        }
        
        .ios-fixed-btn:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,122,255,0.2);
        }
        
        /* iOS图片画廊样式 */
        .ios-gallery {
            position: relative;
            width: 100%;
            height: 300px;
        }
        
        .ios-gallery-counter {
            position: absolute;
            bottom: 12px;
            right: 12px;
            background-color: rgba(0,0,0,0.6);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            color: white;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 10px;
            border-radius: 100px;
            display: flex;
            align-items: center;
        }
        
        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
        }
        
        /* iOS信息行 */
        .ios-info-row {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        
        .ios-info-icon {
            width: 28px;
            color: var(--ios-gray);
            font-size: 14px;
            display: flex;
            justify-content: center;
        }
        
        .ios-info-text {
            flex: 1;
            color: var(--ios-label);
            font-size: 15px;
        }
        
        /* iOS圆角图片 */
        .ios-rounded-image {
            border-radius: 8px;
            overflow: hidden;
        }
        
        /* iOS联系按钮 */
        .ios-contact-btn {
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
            border-radius: 20px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }
        
        .ios-contact-btn:active {
            background-color: rgba(0,122,255,0.2);
        }
        
        /* iOS高亮文本 */
        .ios-highlight-text {
            color: var(--ios-blue);
            font-weight: 500;
        }
        
        /* iOS子标题 */
        .ios-sub-title {
            font-size: 14px;
            color: var(--ios-gray);
            margin-bottom: 4px;
        }
        
        /* iOS图片容器 */
        .ios-image-container {
            border-radius: 8px;
            overflow: hidden;
            background-color: #f2f2f7;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">停车位详情</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='parking-market.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">停车位详情</h1>
                <button onclick="shareParking()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-share-alt text-[#007AFF]"></i>
                </button>
            </div>

            <div class="ios-content-area">
                <!-- 停车位图片 -->
                <div class="ios-gallery relative">
                    <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                    <div class="ios-gallery-counter">
                        <i class="fas fa-image mr-1.5 text-[10px]"></i>
                        <span>1/3</span>
                    </div>
                </div>

                <!-- 基本信息 -->
                <div class="bg-white p-4 ios-fade-in">
                    <div class="flex justify-between items-start">
                        <div>
                            <h2 class="text-xl font-semibold tracking-tight">阳光花园地下停车位</h2>
                            <p class="ios-price text-xl mt-2">¥300<span class="text-sm font-normal">/月</span></p>
                        </div>
                        <button class="flex flex-col items-center ios-button ios-haptic">
                            <i class="fas fa-heart text-[#8E8E93] text-xl"></i>
                            <span class="text-xs text-[#8E8E93] mt-1">收藏</span>
                        </button>
                    </div>
                    <div class="flex flex-wrap gap-2 mt-3">
                        <span class="ios-tag flex items-center">
                            <i class="fas fa-shield-alt text-[10px] mr-1"></i>
                            24小时保安
                        </span>
                        <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">
                            <i class="fas fa-calendar-alt text-[10px] mr-1"></i>
                            可短租
                        </span>
                        <span class="ios-tag" style="background-color: rgba(255,149,0,0.1); color: var(--ios-orange);">
                            <i class="fas fa-subway text-[10px] mr-1"></i>
                            临近地铁
                        </span>
                    </div>
                    <div class="flex items-center mt-3 text-xs text-[#8E8E93]">
                        <span class="mr-4 flex items-center"><i class="fas fa-eye text-[10px] mr-1"></i> 浏览 156</span>
                        <span class="mr-4 flex items-center"><i class="fas fa-heart text-[10px] mr-1"></i> 收藏 12</span>
                        <span class="flex items-center"><i class="fas fa-clock text-[10px] mr-1"></i> 发布于 2024-01-18</span>
                    </div>
                </div>

                <!-- 车位信息 -->
                <div class="bg-white mt-2 p-4 ios-fade-in" style="animation-delay: 0.1s;">
                    <h3 class="ios-section-title mb-3">车位信息</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="ios-info-row">
                            <span class="ios-info-icon"><i class="fas fa-map-marker-alt"></i></span>
                            <span class="ios-info-text">地下负一层 B区</span>
                        </div>
                        <div class="ios-info-row">
                            <span class="ios-info-icon"><i class="fas fa-ruler-combined"></i></span>
                            <span class="ios-info-text">2.4m × 5.4m</span>
                        </div>
                        <div class="ios-info-row">
                            <span class="ios-info-icon"><i class="fas fa-clock"></i></span>
                            <span class="ios-info-text">全天可用</span>
                        </div>
                        <div class="ios-info-row">
                            <span class="ios-info-icon"><i class="fas fa-car"></i></span>
                            <span class="ios-info-text">固定车位</span>
                        </div>
                    </div>
                </div>

                <!-- 出租信息 -->
                <div class="bg-white mt-2 p-4 ios-fade-in" style="animation-delay: 0.15s;">
                    <h3 class="ios-section-title mb-3">出租信息</h3>
                    <div class="space-y-3 text-sm text-[#3A3A3C]">
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-calendar-check"></i></span>
                            <p class="ios-info-text">租期要求：3个月起租</p>
                        </div>
                        <div class="ios-separator mx-6 my-1"></div>
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-money-bill-wave"></i></span>
                            <p class="ios-info-text">押金：1个月</p>
                        </div>
                        <div class="ios-separator mx-6 my-1"></div>
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-credit-card"></i></span>
                            <p class="ios-info-text">支付方式：月付/季付</p>
                        </div>
                        <div class="ios-separator mx-6 my-1"></div>
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-car"></i></span>
                            <p class="ios-info-text">车位类型：固定车位，专人管理</p>
                        </div>
                        <div class="ios-separator mx-6 my-1"></div>
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-shield-alt"></i></span>
                            <p class="ios-info-text">安全保障：24小时保安巡逻，全天监控</p>
                        </div>
                        <div class="ios-separator mx-6 my-1"></div>
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-plug"></i></span>
                            <p class="ios-info-text">配套设施：充电桩（需额外付费）</p>
                        </div>
                        <div class="ios-separator mx-6 my-1"></div>
                        <div class="flex">
                            <span class="ios-info-icon"><i class="fas fa-map-marker-alt"></i></span>
                            <p class="ios-info-text">交通位置：距离地铁2号线阳光花园站500米</p>
                        </div>
                    </div>
                </div>

                <!-- 地理位置 -->
                <div class="bg-white mt-2 p-4 ios-fade-in" style="animation-delay: 0.2s;">
                    <h3 class="ios-section-title mb-3">地理位置</h3>
                    <div class="ios-rounded-image h-36 bg-[#f2f2f7] flex items-center justify-center mb-2">
                        <i class="fas fa-map-marked-alt text-[#8E8E93] text-4xl"></i>
                    </div>
                    <p class="text-sm text-[#3A3A3C] flex items-center">
                        <i class="fas fa-map-marker-alt text-[#FF3B30] mr-2"></i>
                        杭州市拱墅区阳光花园小区地下停车场B区
                    </p>
                </div>

                <!-- 业主信息 -->
                <div class="bg-white mt-2 p-4 ios-fade-in" style="animation-delay: 0.25s;">
                    <div class="flex items-center">
                        <div class="ios-image-container w-12 h-12 overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="w-full h-full object-cover">
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex items-center">
                                <h3 class="font-medium">张先生</h3>
                                <span class="ml-2 ios-tag text-xs flex items-center">
                                    <i class="fas fa-check-circle text-[10px] mr-1"></i>
                                    已实名
                                </span>
                            </div>
                            <p class="text-xs text-[#8E8E93] mt-1">阳光花园业主</p>
                        </div>
                        <button class="ios-contact-btn ios-button ios-haptic flex items-center">
                            <i class="fas fa-comment mr-1"></i>
                            联系业主
                        </button>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav p-4 z-40">
                <div class="flex space-x-3">
                    <button class="ios-button ios-haptic flex flex-col items-center justify-center w-12">
                        <i class="fas fa-phone-alt text-[#007AFF] text-xl"></i>
                        <span class="text-xs text-[#007AFF] mt-1">电话</span>
                    </button>
                    <button class="ios-fixed-btn ios-button ios-haptic flex-1">联系业主</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 图片滑动效果初始化
            // 这里可以添加轮播图功能
            
            // 分享功能
            window.shareParking = function() {
                // 在支持的设备上调用原生分享API
                if (navigator.share) {
                    navigator.share({
                        title: '阳光花园地下停车位',
                        text: '月租300元的地下停车位，位于阳光花园小区',
                        url: window.location.href,
                    })
                    .catch((error) => console.log('分享失败:', error));
                } else {
                    // 如果不支持原生分享，显示自定义分享菜单
                    alert('分享功能暂不可用');
                }
            };
        });
    </script>
</body>
</html> 
</html> 