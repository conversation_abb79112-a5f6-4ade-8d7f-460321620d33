<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 农户详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }

        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-large);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            margin: 12px 16px;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--ios-label);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: 0.5px solid var(--ios-separator);
        }

        /* iOS信息行 */
        .ios-info-row {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        
        .ios-info-icon {
            color: var(--ios-secondaryLabel);
            width: 24px;
            display: flex;
            justify-content: center;
        }

        /* iOS认证标签 */
        .ios-badge {
            display: inline-flex;
            align-items: center;
            background-color: rgba(52,199,89,0.1);
            color: var(--ios-green);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* iOS数据卡片 */
        .ios-stat-card {
            text-align: center;
        }
        
        .ios-stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-blue);
            margin-bottom: 2px;
        }
        
        .ios-stat-label {
            font-size: 12px;
            color: var(--ios-secondaryLabel);
        }

        /* iOS分类按钮 */
        .ios-segment-button {
            padding: 8px 16px;
            font-size: 14px;
            border: 0.5px solid var(--ios-separator);
            transition: all 0.2s ease;
        }
        
        .ios-segment-button:first-child {
            border-top-left-radius: var(--ios-corner-radius-large);
            border-bottom-left-radius: var(--ios-corner-radius-large);
            border-right: none;
        }
        
        .ios-segment-button:last-child {
            border-top-right-radius: var(--ios-corner-radius-large);
            border-bottom-right-radius: var(--ios-corner-radius-large);
            border-left: none;
        }
        
        .ios-segment-button.active {
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }

        /* iOS商品项 */
        .ios-product-item {
            background-color: var(--ios-secondarySystemBackground);
            border-radius: var(--ios-corner-radius-medium);
            overflow: hidden;
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }

        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: var(--ios-separator);
            width: 100%;
        }
        
        /* iOS标签项 */
        .ios-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            background-color: white;
            border-radius: var(--ios-corner-radius-medium);
            font-size: 12px;
            color: var(--ios-secondaryLabel);
        }
        
        .ios-tag i {
            margin-right: 4px;
            color: var(--ios-green);
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
        
        .category-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">农户详情</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="ios-nav-title">农户详情</div>
                <button class="ios-button ios-haptic text-[var(--ios-secondaryLabel)]">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
            
            <div class="screen-content pb-20">
                <!-- 农户头图 -->
                <div class="relative ios-fade-in ios-fade-in-delay-1">
                    <div class="w-full h-64 bg-[var(--ios-secondarySystemBackground)] relative overflow-hidden">
                        <!-- 主背景图 -->
                        <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef" 
                            class="w-full h-full object-cover">
                        <!-- 四角模糊效果 -->
                        <div class="absolute inset-0" 
                            style="
                                background: 
                                    radial-gradient(circle at 0 0, rgba(0,0,0,0.2) 0%, transparent 30%),
                                    radial-gradient(circle at 100% 0, rgba(0,0,0,0.2) 0%, transparent 30%),
                                    radial-gradient(circle at 0 100%, rgba(0,0,0,0.2) 0%, transparent 30%),
                                    radial-gradient(circle at 100% 100%, rgba(0,0,0,0.2) 0%, transparent 30%);
                                backdrop-filter: blur(12px);
                                -webkit-backdrop-filter: blur(12px);
                            ">
                        </div>
                        <!-- 轻微渐变遮罩 -->
                        <div class="absolute inset-0 bg-gradient-to-b from-black/5 to-black/30"></div>
                    </div>
                    <!-- 头像和基本信息 -->
                    <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                        <div class="flex items-start">
                            <img src="https://images.unsplash.com/photo-1595152772835-219674b2a8a6" 
                                class="w-20 h-20 rounded-full border-2 border-white object-cover shadow-lg relative z-10">
                            <div class="ml-4 flex-1">
                                <div class="flex items-center">
                                    <h2 class="text-xl font-semibold mr-2 text-shadow-sm">张大叔家的农场</h2>
                                    <div class="ios-badge bg-white/20 backdrop-blur-sm">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        <span class="text-xs">已认证</span>
                                    </div>
                                </div>
                                <div class="flex items-center mt-2">
                                    <div class="flex space-x-0.5">
                                        <i class="fas fa-star text-yellow-400 text-xs drop-shadow-sm"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs drop-shadow-sm"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs drop-shadow-sm"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs drop-shadow-sm"></i>
                                        <i class="fas fa-star-half-alt text-yellow-400 text-xs drop-shadow-sm"></i>
                                    </div>
                                    <span class="text-xs text-white/90 ml-2 text-shadow-sm">4.8 (128评)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 农户信息 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-2">
                    <div class="p-5">
                        <!-- 统计数据 -->
                        <div class="grid grid-cols-3 gap-6">
                            <div class="ios-stat-card">
                                <p class="ios-stat-value text-xl">156</p>
                                <p class="ios-stat-label">总成交量</p>
                            </div>
                            <div class="ios-stat-card" style="border-left: 1px solid var(--ios-separator); border-right: 1px solid var(--ios-separator)">
                                <p class="ios-stat-value text-xl">98.7%</p>
                                <p class="ios-stat-label">好评率</p>
                            </div>
                            <div class="ios-stat-card">
                                <p class="ios-stat-value text-xl">12</p>
                                <p class="ios-stat-label">在售商品</p>
                            </div>
                        </div>

                        <!-- 联系信息 -->
                        <div class="space-y-2.5">
                            <div class="ios-info-row">
                                <i class="fas fa-map-marker-alt ios-info-icon text-sm text-[var(--ios-secondaryLabel)]"></i>
                                <div class="ml-2">
                                    <span class="text-sm">杭州市临平区东湖街道田园路68号</span>
                                    <span class="text-xs text-[var(--ios-blue)] ml-2">1.2km</span>
                                </div>
                            </div>
                            <div class="ios-info-row">
                                <i class="fas fa-phone ios-info-icon text-sm text-[var(--ios-secondaryLabel)]"></i>
                                <div class="ml-2">
                                    <span class="text-sm">138****6688</span>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)] ml-2">点击拨打</span>
                                </div>
                            </div>
                            <div class="ios-info-row">
                                <i class="fas fa-clock ios-info-icon text-sm text-[var(--ios-secondaryLabel)]"></i>
                                <div class="ml-2">
                                    <span class="text-sm">配送时间：每日8:00-18:00</span>
                                </div>
                            </div>
                            <div class="ios-info-row">
                                <i class="fas fa-truck ios-info-icon text-sm text-[var(--ios-secondaryLabel)]"></i>
                                <div class="ml-2">
                                    <span class="text-sm">配送范围：临平区及周边5公里范围内</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 认证信息 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-3">
                    <div class="p-4">
                        <h3 class="font-medium text-base mb-3">认证信息</h3>
                        <div class="bg-[rgba(52,199,89,0.05)] p-4 rounded-xl">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-[rgba(52,199,89,0.1)] rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-shield-alt text-[var(--ios-green)]"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">实名认证农户</h4>
                                    <p class="text-xs text-[var(--ios-secondaryLabel)] mt-1">已通过农业经营户实名认证，确保交易安全</p>
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-2 mt-3">
                                <div class="ios-tag">
                                    <i class="fas fa-id-card"></i>
                                    <span>身份证已验证</span>
                                </div>
                                <div class="ios-tag">
                                    <i class="fas fa-phone-alt"></i>
                                    <span>手机号已验证</span>
                                </div>
                                <div class="ios-tag">
                                    <i class="fas fa-certificate"></i>
                                    <span>持有农业经营证</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 农场介绍 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-3">
                    <div class="p-4">
                        <h3 class="font-medium text-base mb-3">农场介绍</h3>
                        <div class="text-sm text-[var(--ios-secondaryLabel)] space-y-3">
                            <p>我家的农场位于临平区东湖街道，是一个小型的家庭农场，主要从事家禽家畜养殖和蔬菜种植已有15年。</p>
                            <p>我家的土鸡都是自然散养，采用传统喂养方式，不添加任何生长激素，鸡吃的是自然谷物和虫子，肉质特别好，煲汤特别香。</p>
                            <p>除了土鸡，我们还有土鸡蛋、鸭子、鹅以及自种的时令蔬菜，都是无公害种植，绿色健康，欢迎大家选购！</p>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mt-3">
                            <img src="https://images.unsplash.com/photo-1587486913049-53fc88980cfc" class="w-full aspect-square object-cover rounded-lg">
                            <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef" class="w-full aspect-square object-cover rounded-lg">
                            <img src="https://images.unsplash.com/photo-1499529112087-338cba0ad803" class="w-full aspect-square object-cover rounded-lg">
                        </div>
                    </div>
                </div>

                <!-- 在售商品 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-4">
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="font-medium text-base">在售商品</h3>
                            <div class="flex">
                                <button class="ios-segment-button active ios-button ios-haptic">
                                    全部
                                </button>
                                <button class="ios-segment-button ios-button ios-haptic">
                                    热销
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div class="ios-product-item ios-button ios-haptic p-3" onclick="window.location.href='product-detail.html?id=1'">
                                <div class="relative">
                                    <img src="https://images.unsplash.com/photo-1518492104633-130d0cc84637" class="w-full aspect-square object-cover rounded-lg mb-2">
                                    <span class="absolute top-2 left-2 text-xs bg-[var(--ios-red)] text-white px-2 py-1 rounded-full">热卖</span>
                                </div>
                                <h4 class="text-sm font-medium">散养土鸡</h4>
                                <div class="flex justify-between items-center mt-1">
                                    <p class="text-sm text-[var(--ios-red)] font-medium">¥38.8/只</p>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">已售92件</span>
                                </div>
                            </div>
                            <div class="ios-product-item ios-button ios-haptic p-3" onclick="window.location.href='product-detail.html?id=3'">
                                <img src="https://images.unsplash.com/photo-1598103442097-8b74394b95c6" class="w-full aspect-square object-cover rounded-lg mb-2">
                                <h4 class="text-sm font-medium">散养土鸡蛋</h4>
                                <div class="flex justify-between items-center mt-1">
                                    <p class="text-sm text-[var(--ios-red)] font-medium">¥3.5/个</p>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">已售108件</span>
                                </div>
                            </div>
                            <div class="ios-product-item ios-button ios-haptic p-3" onclick="window.location.href='product-detail.html?id=5'">
                                <img src="https://images.unsplash.com/photo-1511044568932-338cba0ad803" class="w-full aspect-square object-cover rounded-lg mb-2">
                                <h4 class="text-sm font-medium">散养鸭子</h4>
                                <div class="flex justify-between items-center mt-1">
                                    <p class="text-sm text-[var(--ios-red)] font-medium">¥46.8/只</p>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">已售35件</span>
                                </div>
                            </div>
                            <div class="ios-product-item ios-button ios-haptic p-3" onclick="window.location.href='product-detail.html?id=4'">
                                <img src="https://images.unsplash.com/photo-1598788841836-739cab3b3f95" class="w-full aspect-square object-cover rounded-lg mb-2">
                                <h4 class="text-sm font-medium">自种有机蔬菜</h4>
                                <div class="flex justify-between items-center mt-1">
                                    <p class="text-sm text-[var(--ios-red)] font-medium">¥15.8/份</p>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">已售92件</span>
                                </div>
                            </div>
                        </div>

                        <button class="w-full mt-4 py-2.5 border border-[var(--ios-separator)] rounded-xl text-[var(--ios-secondaryLabel)] text-sm ios-button ios-haptic">
                            查看全部12件商品
                        </button>
                    </div>
                </div>

                <!-- 用户评价 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-4 mb-6">
                    <div class="p-4">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium text-base">用户评价</h3>
                            <a href="farmer-reviews.html?id=1" class="ios-button ios-haptic text-sm text-[var(--ios-blue)] flex items-center">
                                查看全部128条
                                <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>

                        <div class="mt-3 space-y-4">
                            <div class="pb-3 border-b border-[var(--ios-separator)]">
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956" class="w-8 h-8 rounded-full mr-2 object-cover">
                                    <span>李**</span>
                                    <div class="flex ml-3">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-sm mt-2">张大叔家的土鸡真的不错，肉质紧实有嚼劲，熬汤特别香浓。最重要的是送货上门很及时，出门在外没时间买菜的人特别方便！</p>
                                <div class="text-xs text-[var(--ios-secondaryLabel)] mt-2">2025-03-15</div>
                            </div>

                            <div>
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1595152772835-219674b2a8a6" class="w-8 h-8 rounded-full mr-2 object-cover">
                                    <span>王**</span>
                                    <div class="flex ml-3">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-gray-300 text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-sm mt-2">这家的农产品很新鲜，价格也合理。每周都会买一些回来，很满意。就是有时候配送时间不是很准，希望能改进一下。</p>
                                <div class="text-xs text-[var(--ios-secondaryLabel)] mt-2">2025-03-10</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="fixed bottom-0 left-0 right-0 bg-white border-t flex p-3">
                <button class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-gray-500 text-sm mr-3">
                    <i class="far fa-heart mr-1"></i>
                    收藏
                </button>
                <button class="flex-1 bg-blue-500 text-white py-2 rounded-lg text-sm" id="contactBtn">
                    联系农户
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 分类选项切换
            const segmentButtons = document.querySelectorAll('.ios-segment-button');
            segmentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 添加触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([5, 15]);
                    }
                    
                    // 清除所有active状态
                    segmentButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // 设置当前按钮为active
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 