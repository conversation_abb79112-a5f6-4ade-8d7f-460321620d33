<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 集市订单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }

        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-large);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--ios-label);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: 0.5px solid var(--ios-separator);
        }

        /* iOS标签切换栏 */
        .ios-tab-bar {
            display: flex;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-tab {
            padding: 12px 0;
            text-align: center;
            flex: 1;
            font-size: 15px;
            color: var(--ios-secondaryLabel);
            transition: all 0.2s ease;
            position: relative;
        }
        
        .ios-tab.active {
            color: var(--ios-blue);
            font-weight: 500;
        }
        
        .ios-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 25%;
            width: 50%;
            height: 2px;
            background-color: var(--ios-blue);
            border-radius: 1px;
        }

        /* iOS订单状态标签 */
        .ios-order-status {
            font-size: 14px;
            font-weight: 500;
        }
        
        .ios-order-status.pending {
            color: var(--ios-red);
        }
        
        .ios-order-status.shipping {
            color: var(--ios-green);
        }
        
        .ios-order-status.completed {
            color: var(--ios-gray);
        }

        /* iOS订单按钮 */
        .ios-order-button {
            border-radius: 18px;
            font-size: 13px;
            padding: 6px 16px;
            border: 0.5px solid var(--ios-separator);
            color: var(--ios-secondaryLabel);
            white-space: nowrap;
            min-width: 76px;
            text-align: center;
        }
        
        .ios-order-button.primary {
            background-color: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }
        
        .ios-order-button.outlined {
            border-color: var(--ios-blue);
            color: var(--ios-blue);
        }

        /* iOS订单项 */
        .ios-order-item {
            border-radius: var(--ios-corner-radius-large);
            background-color: var(--ios-systemBackground);
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.03);
            margin-bottom: 12px;
        }

        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }

        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: var(--ios-separator);
            width: 100%;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
        
        .category-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">我的订单</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="ios-nav-title">我的订单</div>
                <button class="ios-button ios-haptic text-[var(--ios-blue)]">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- 订单状态选项卡 -->
            <div class="ios-tab-bar">
                <button class="ios-tab active ios-button ios-haptic" data-tab="all">全部</button>
                <button class="ios-tab ios-button ios-haptic" data-tab="pending">待付款</button>
                <button class="ios-tab ios-button ios-haptic" data-tab="shipping">待收货</button>
                <button class="ios-tab ios-button ios-haptic" data-tab="completed">已完成</button>
            </div>

            <!-- 订单列表 -->
            <div class="p-4 ios-fade-in ios-fade-in-delay-1">
                <!-- 待付款订单 -->
                <div class="ios-order-item mb-4 ios-fade-in ios-fade-in-delay-2" data-type="pending">
                    <div class="p-3 border-b border-[var(--ios-separator)] flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-store text-[var(--ios-secondaryLabel)] mr-2"></i>
                            <span class="font-medium">王大伯家庭农场</span>
                        </div>
                        <span class="ios-order-status pending">待付款</span>
                    </div>
                    <div class="p-3 flex border-b border-[var(--ios-separator)]">
                        <img src="https://via.placeholder.com/80" class="w-20 h-20 rounded-lg object-cover" alt="散养土鸡">
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <h3 class="font-medium">散养土鸡</h3>
                                <span class="text-[var(--ios-secondaryLabel)]">¥38.8</span>
                            </div>
                            <p class="text-[var(--ios-secondaryLabel)] text-sm mt-1">规格：整鸡（约2斤）</p>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-sm">自行配送</span>
                                <span class="text-sm">x1</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 flex justify-between items-center">
                        <div class="text-[var(--ios-secondaryLabel)] text-sm">
                            下单时间：2025-04-03 10:28
                        </div>
                        <div class="text-right">
                            <div class="mb-2 text-sm">合计：<span class="text-[var(--ios-red)] font-medium">¥38.8</span></div>
                            <div class="flex space-x-2">
                                <button class="ios-order-button ios-button ios-haptic">取消订单</button>
                                <button class="ios-order-button primary ios-button ios-haptic">立即付款</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 待收货订单 -->
                <div class="ios-order-item mb-4 ios-fade-in ios-fade-in-delay-3" data-type="shipping">
                    <div class="p-3 border-b border-[var(--ios-separator)] flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-store text-[var(--ios-secondaryLabel)] mr-2"></i>
                            <span class="font-medium">李阿姨家庭农场</span>
                        </div>
                        <span class="ios-order-status shipping">待收货</span>
                    </div>
                    <div class="p-3 flex border-b border-[var(--ios-separator)]">
                        <img src="https://via.placeholder.com/80" class="w-20 h-20 rounded-lg object-cover" alt="鲜鱼">
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <h3 class="font-medium">鲜活草鱼</h3>
                                <span class="text-[var(--ios-secondaryLabel)]">¥22.5</span>
                            </div>
                            <p class="text-[var(--ios-secondaryLabel)] text-sm mt-1">规格：约2斤/条</p>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-sm">自行配送</span>
                                <span class="text-sm">x2</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 flex justify-between items-center">
                        <div class="text-[var(--ios-secondaryLabel)] text-sm">
                            下单时间：2025-04-02 16:15
                        </div>
                        <div class="text-right">
                            <div class="mb-2 text-sm">合计：<span class="text-[var(--ios-red)] font-medium">¥45.0</span></div>
                            <div class="flex space-x-2">
                                <button class="ios-order-button ios-button ios-haptic">查看物流</button>
                                <button class="ios-order-button primary ios-button ios-haptic">确认收货</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 已完成订单 -->
                <div class="ios-order-item mb-4 ios-fade-in ios-fade-in-delay-4" data-type="completed">
                    <div class="p-3 border-b border-[var(--ios-separator)] flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-store text-[var(--ios-secondaryLabel)] mr-2"></i>
                            <span class="font-medium">张叔叔生态农场</span>
                        </div>
                        <span class="ios-order-status completed">已完成</span>
                    </div>
                    <div class="p-3 flex border-b border-[var(--ios-separator)]">
                        <img src="https://via.placeholder.com/80" class="w-20 h-20 rounded-lg object-cover" alt="鸭蛋">
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <h3 class="font-medium">散养鸭蛋</h3>
                                <span class="text-[var(--ios-secondaryLabel)]">¥3.5</span>
                            </div>
                            <p class="text-[var(--ios-secondaryLabel)] text-sm mt-1">规格：单枚</p>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-sm">顾客自提</span>
                                <span class="text-sm">x10</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 flex justify-between items-center">
                        <div class="text-[var(--ios-secondaryLabel)] text-sm">
                            完成时间：2025-04-01 09:45
                        </div>
                        <div class="text-right">
                            <div class="mb-2 text-sm">合计：<span class="text-[var(--ios-red)] font-medium">¥35.0</span></div>
                            <div class="flex space-x-2">
                                <button class="ios-order-button ios-button ios-haptic">再次购买</button>
                                <button class="ios-order-button outlined ios-button ios-haptic">评价</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 已完成订单2 -->
                <div class="ios-order-item mb-4 ios-fade-in ios-fade-in-delay-4" data-type="completed">
                    <div class="p-3 border-b border-[var(--ios-separator)] flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-store text-[var(--ios-secondaryLabel)] mr-2"></i>
                            <span class="font-medium">周奶奶家庭菜园</span>
                        </div>
                        <span class="ios-order-status completed">已完成</span>
                    </div>
                    <div class="p-3 flex border-b border-[var(--ios-separator)]">
                        <img src="https://via.placeholder.com/80" class="w-20 h-20 rounded-lg object-cover" alt="蔬菜">
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <h3 class="font-medium">新鲜小油菜</h3>
                                <span class="text-[var(--ios-secondaryLabel)]">¥5.8</span>
                            </div>
                            <p class="text-[var(--ios-secondaryLabel)] text-sm mt-1">规格：一把约500g</p>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-sm">自行配送</span>
                                <span class="text-sm">x2</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 flex justify-between items-center">
                        <div class="text-[var(--ios-secondaryLabel)] text-sm">
                            完成时间：2025-03-28 15:30
                        </div>
                        <div class="text-right">
                            <div class="mb-2 text-sm">合计：<span class="text-[var(--ios-red)] font-medium">¥11.6</span></div>
                            <div class="flex space-x-2">
                                <button class="ios-order-button ios-button ios-haptic">再次购买</button>
                                <button class="ios-order-button ios-button ios-haptic">已评价</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav py-2">
                <div class="flex justify-around">
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-home text-[var(--ios-secondaryLabel)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-secondaryLabel)] mt-1">首页</span>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-compass text-[var(--ios-blue)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-blue)] mt-1">发现</span>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <div class="w-12 h-12 bg-[var(--ios-blue)] rounded-full flex items-center justify-center -mt-4">
                            <i class="fas fa-plus text-white text-xl"></i>
                        </div>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-comment text-[var(--ios-secondaryLabel)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-secondaryLabel)] mt-1">消息</span>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-user text-[var(--ios-secondaryLabel)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-secondaryLabel)] mt-1">我的</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 订单状态选项卡切换
            const tabButtons = document.querySelectorAll('.ios-tab');
            const orderItems = document.querySelectorAll('.ios-order-item');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 添加触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([8, 12]);
                    }
                    
                    // 重置所有按钮样式
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // 设置当前按钮样式
                    this.classList.add('active');
                    
                    // 筛选订单
                    const tabType = this.dataset.tab;
                    orderItems.forEach(item => {
                        if (tabType === 'all' || item.dataset.type === tabType) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
            
            // 订单操作按钮效果增强
            const orderButtons = document.querySelectorAll('.ios-order-button');
            orderButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 添加较强的触感反馈
                    if ('vibrate' in navigator) {
                        if (this.classList.contains('primary')) {
                            navigator.vibrate([10, 20, 10]); // 更强的振动
                        } else {
                            navigator.vibrate([5, 15]);
                        }
                    }
                    
                    // 防止冒泡到卡片
                    e.stopPropagation();
                });
            });
        });
    </script>
</body>
</html> 