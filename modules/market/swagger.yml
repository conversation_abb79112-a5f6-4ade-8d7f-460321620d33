openapi: 3.0.3
info:
  title: 乐享友邻 - 邻里集市API
  description: 邻里集市平台后端接口文档，包含农产品交易、农户管理、订单处理等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  /market/products:
    post:
      tags:
        - 商品管理
      summary: 获取商品列表
      description: 根据分类、位置等条件获取农产品列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductListRequest'
      responses:
        '200':
          description: 成功获取商品列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/products/{productId}:
    get:
      tags:
        - 商品管理
      summary: 获取商品详情
      description: 根据商品ID获取农产品的详细信息
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 成功获取商品详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductDetailResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/products/publish:
    post:
      tags:
        - 商品管理
      summary: 发布农产品
      description: 农户发布新的农产品信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishProductRequest'
      responses:
        '201':
          description: 商品发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishProductResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/products/search:
    post:
      tags:
        - 商品搜索
      summary: 搜索农产品
      description: 根据关键词搜索农产品
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/products/categories:
    get:
      tags:
        - 商品分类
      summary: 获取商品分类列表
      description: 获取所有农产品分类信息
      responses:
        '200':
          description: 成功获取分类列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesResponse'

  /market/products/hot:
    get:
      tags:
        - 商品推荐
      summary: 获取热卖商品
      description: 获取当前热卖的农产品列表
      parameters:
        - name: limit
          in: query
          description: 返回商品数量限制
          schema:
            type: integer
            minimum: 1
            maximum: 20
            default: 10
      responses:
        '200':
          description: 成功获取热卖商品
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotProductsResponse'

  /market/farmers:
    post:
      tags:
        - 农户管理
      summary: 获取农户列表
      description: 根据位置获取附近的农户列表
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FarmerListRequest'
      responses:
        '200':
          description: 成功获取农户列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FarmerListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/farmers/{farmerId}:
    get:
      tags:
        - 农户管理
      summary: 获取农户详情
      description: 根据农户ID获取农户的详细信息和商品列表
      parameters:
        - name: farmerId
          in: path
          required: true
          description: 农户唯一标识ID
          schema:
            type: string
            example: "farmer_123456"
      responses:
        '200':
          description: 成功获取农户详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FarmerDetailResponse'
        '404':
          description: 农户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/orders:
    post:
      tags:
        - 订单管理
      summary: 创建订单
      description: 用户创建农产品购买订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '201':
          description: 订单创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    get:
      tags:
        - 订单管理
      summary: 获取用户订单列表
      description: 获取当前用户的订单列表
      parameters:
        - name: status
          in: query
          description: 订单状态筛选
          schema:
            type: string
            enum: [all, pending, confirmed, preparing, delivering, completed, cancelled]
            default: all
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: 成功获取订单列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderListResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/orders/{orderId}:
    get:
      tags:
        - 订单管理
      summary: 获取订单详情
      description: 根据订单ID获取订单的详细信息
      parameters:
        - name: orderId
          in: path
          required: true
          description: 订单唯一标识ID
          schema:
            type: string
            example: "order_123456"
      responses:
        '200':
          description: 成功获取订单详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetailResponse'
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /market/orders/{orderId}/cancel:
    post:
      tags:
        - 订单管理
      summary: 取消订单
      description: 用户取消订单
      parameters:
        - name: orderId
          in: path
          required: true
          description: 订单唯一标识ID
          schema:
            type: string
            example: "order_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: 取消原因
                  example: "不需要了"
      responses:
        '200':
          description: 订单取消成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 订单状态不允许取消
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /upload/product-images:
    post:
      tags:
        - 文件上传
      summary: 上传商品图片
      description: 上传农产品相关图片，支持多张图片上传
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 图片文件数组，最多9张
                isMainImage:
                  type: boolean
                  description: 是否为主图
                  default: false
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: 上传失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ProductListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            category:
              type: string
              description: 商品分类
              example: "家禽家畜"
              enum: ["全部", "家禽家畜", "鱼虾水产", "蔬菜瓜果", "禽蛋", "其他"]
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低价格
                  example: 10
                max:
                  type: number
                  description: 最高价格
                  example: 100
            location:
              type: object
              properties:
                latitude:
                  type: number
                  description: 用户位置纬度
                  example: 30.2741
                longitude:
                  type: number
                  description: 用户位置经度
                  example: 120.1551
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 10
                  default: 10
            freshness:
              type: string
              description: 新鲜度要求
              example: "当日采摘"
              enum: ["不限", "当日采摘", "3天内", "一周内"]
        sortBy:
          type: string
          enum: [comprehensive, price_asc, price_desc, distance, sales, rating]
          default: comprehensive
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          category: "家禽家畜"
          priceRange:
            min: 20
            max: 80
          location:
            latitude: 30.2741
            longitude: 120.1551
            radius: 10
          freshness: "当日采摘"
        sortBy: "comprehensive"

    ProductListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 156
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            totalPages:
              type: integer
              description: 总页数
              example: 8
            products:
              type: array
              items:
                $ref: '#/components/schemas/ProductItem'

    ProductItem:
      type: object
      properties:
        id:
          type: string
          description: 商品ID
          example: "product_123456"
        name:
          type: string
          description: 商品名称
          example: "散养土鸡"
        description:
          type: string
          description: 商品描述
          example: "王阿姨家散养 | 现杀现发"
        price:
          type: number
          description: 价格
          example: 38.8
        unit:
          type: string
          description: 计量单位
          example: "只"
        category:
          type: string
          description: 商品分类
          example: "家禽家畜"
        mainImage:
          type: string
          description: 主图URL
          example: "https://images.example.com/chicken1.jpg"
        imageCount:
          type: integer
          description: 图片总数
          example: 5
        tags:
          type: array
          items:
            type: string
          description: 商品标签
          example: ["热卖", "现杀现发", "散养"]
        farmer:
          type: object
          properties:
            id:
              type: string
              description: 农户ID
              example: "farmer_789"
            name:
              type: string
              description: 农户名称
              example: "张大叔家的农场"
            avatar:
              type: string
              description: 农户头像
              example: "https://images.example.com/farmer1.jpg"
            distance:
              type: number
              description: 距离（公里）
              example: 1.2
            rating:
              type: number
              description: 评分
              example: 4.8
        salesCount:
          type: integer
          description: 销量
          example: 52
        stock:
          type: integer
          description: 库存数量
          example: 20
        freshness:
          type: string
          description: 新鲜度
          example: "当日采摘"
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        isAvailable:
          type: boolean
          description: 是否有货
          example: true

    ProductDetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          $ref: '#/components/schemas/ProductDetail'

    ProductDetail:
      type: object
      properties:
        id:
          type: string
          description: 商品ID
          example: "product_123456"
        name:
          type: string
          description: 商品名称
          example: "散养土鸡"
        description:
          type: string
          description: 商品详细描述
          example: "王阿姨家散养土鸡，在山区自然环境中散养，喂食玉米、稻谷等天然饲料..."
        price:
          type: number
          description: 价格
          example: 38.8
        unit:
          type: string
          description: 计量单位
          example: "只"
        category:
          type: string
          description: 商品分类
          example: "家禽家畜"
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 图片URL
                example: "https://images.example.com/chicken1_1.jpg"
              isMain:
                type: boolean
                description: 是否为主图
                example: true
              description:
                type: string
                description: 图片描述
                example: "散养环境"
        specifications:
          type: object
          properties:
            weight:
              type: string
              description: 重量规格
              example: "2.5-3斤/只"
            origin:
              type: string
              description: 产地
              example: "杭州市临安区"
            breedingMethod:
              type: string
              description: 养殖方式
              example: "山区散养"
            feedType:
              type: string
              description: 饲料类型
              example: "玉米、稻谷等天然饲料"
            slaughterMethod:
              type: string
              description: 宰杀方式
              example: "现杀现发"
        tags:
          type: array
          items:
            type: string
          description: 商品标签
          example: ["热卖", "现杀现发", "散养", "无抗生素"]
        farmer:
          type: object
          properties:
            id:
              type: string
              description: 农户ID
              example: "farmer_789"
            name:
              type: string
              description: 农户名称
              example: "张大叔家的农场"
            avatar:
              type: string
              description: 农户头像
              example: "https://images.example.com/farmer1.jpg"
            description:
              type: string
              description: 农户介绍
              example: "从事养殖业20年，专注散养土鸡和有机蔬菜种植"
            location:
              type: string
              description: 农户位置
              example: "杭州市临安区"
            distance:
              type: number
              description: 距离（公里）
              example: 1.2
            rating:
              type: number
              description: 评分
              example: 4.8
            reviewCount:
              type: integer
              description: 评价数量
              example: 128
            phone:
              type: string
              description: 联系电话（脱敏）
              example: "138****6789"
        salesCount:
          type: integer
          description: 销量
          example: 52
        stock:
          type: integer
          description: 库存数量
          example: 20
        freshness:
          type: string
          description: 新鲜度
          example: "当日采摘"
        deliveryInfo:
          type: object
          properties:
            methods:
              type: array
              items:
                type: string
              description: 配送方式
              example: ["自提", "同城配送"]
            freeDeliveryThreshold:
              type: number
              description: 免费配送门槛
              example: 50
            deliveryFee:
              type: number
              description: 配送费用
              example: 5
            estimatedTime:
              type: string
              description: 预计配送时间
              example: "下单后2小时内"
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        viewCount:
          type: integer
          description: 浏览次数
          example: 256
        isAvailable:
          type: boolean
          description: 是否有货
          example: true
        isFavorite:
          type: boolean
          description: 是否已收藏
          example: false

    PublishProductRequest:
      type: object
      required:
        - name
        - price
        - unit
        - category
        - images
        - stock
      properties:
        name:
          type: string
          description: 商品名称
          example: "散养土鸡"
          maxLength: 100
        description:
          type: string
          description: 商品详细描述
          example: "山区散养土鸡，喂食天然饲料，肉质鲜美..."
          maxLength: 1000
        price:
          type: number
          description: 价格
          example: 38.8
          minimum: 0
        unit:
          type: string
          description: 计量单位
          example: "只"
          enum: ["只", "斤", "公斤", "箱", "袋", "个"]
        category:
          type: string
          description: 商品分类
          example: "家禽家畜"
          enum: ["家禽家畜", "鱼虾水产", "蔬菜瓜果", "禽蛋", "其他"]
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 图片URL
                example: "https://images.example.com/chicken1_1.jpg"
              isMain:
                type: boolean
                description: 是否为主图
                example: true
              description:
                type: string
                description: 图片描述
                example: "散养环境"
          maxItems: 9
          minItems: 1
        specifications:
          type: object
          properties:
            weight:
              type: string
              description: 重量规格
              example: "2.5-3斤/只"
            origin:
              type: string
              description: 产地
              example: "杭州市临安区"
            breedingMethod:
              type: string
              description: 养殖方式
              example: "山区散养"
            feedType:
              type: string
              description: 饲料类型
              example: "玉米、稻谷等天然饲料"
            slaughterMethod:
              type: string
              description: 宰杀方式
              example: "现杀现发"
        tags:
          type: array
          items:
            type: string
          description: 商品标签
          example: ["现杀现发", "散养", "无抗生素"]
        stock:
          type: integer
          description: 库存数量
          example: 20
          minimum: 1
        freshness:
          type: string
          description: 新鲜度
          example: "当日采摘"
          enum: ["当日采摘", "3天内", "一周内"]
        deliveryInfo:
          type: object
          properties:
            methods:
              type: array
              items:
                type: string
              description: 配送方式
              example: ["自提", "同城配送"]
              enum: ["自提", "同城配送", "快递配送"]
            freeDeliveryThreshold:
              type: number
              description: 免费配送门槛
              example: 50
            deliveryFee:
              type: number
              description: 配送费用
              example: 5
            estimatedTime:
              type: string
              description: 预计配送时间
              example: "下单后2小时内"
      example:
        name: "散养土鸡"
        description: "山区散养土鸡，喂食天然饲料，肉质鲜美..."
        price: 38.8
        unit: "只"
        category: "家禽家畜"
        images:
          - url: "https://images.example.com/chicken1_1.jpg"
            isMain: true
            description: "散养环境"
        specifications:
          weight: "2.5-3斤/只"
          origin: "杭州市临安区"
          breedingMethod: "山区散养"
          feedType: "玉米、稻谷等天然饲料"
          slaughterMethod: "现杀现发"
        tags: ["现杀现发", "散养", "无抗生素"]
        stock: 20
        freshness: "当日采摘"
        deliveryInfo:
          methods: ["自提", "同城配送"]
          freeDeliveryThreshold: 50
          deliveryFee: 5
          estimatedTime: "下单后2小时内"

    PublishProductResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "商品发布成功"
          description: 响应消息
        data:
          type: object
          properties:
            productId:
              type: string
              description: 新创建的商品ID
              example: "product_789012"
            status:
              type: string
              description: 商品状态
              example: "published"
              enum: ["pending_review", "published", "rejected", "sold_out"]
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"

    ProductSearchRequest:
      type: object
      properties:
        keyword:
          type: string
          description: 搜索关键词
          example: "土鸡"
          maxLength: 100
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            category:
              type: string
              description: 商品分类
              example: "家禽家畜"
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低价格
                  example: 20
                max:
                  type: number
                  description: 最高价格
                  example: 80
            location:
              type: object
              properties:
                latitude:
                  type: number
                  description: 用户位置纬度
                  example: 30.2741
                longitude:
                  type: number
                  description: 用户位置经度
                  example: 120.1551
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 10
      example:
        keyword: "土鸡"
        page: 1
        pageSize: 20
        filters:
          category: "家禽家畜"
          priceRange:
            min: 20
            max: 80

    CategoriesResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            categories:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 分类ID
                    example: "category_poultry"
                  name:
                    type: string
                    description: 分类名称
                    example: "家禽家畜"
                  icon:
                    type: string
                    description: 分类图标
                    example: "fas fa-drumstick-bite"
                  color:
                    type: string
                    description: 分类颜色
                    example: "#FF6B6B"
                  productCount:
                    type: integer
                    description: 该分类下的商品数量
                    example: 156

    HotProductsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            products:
              type: array
              items:
                $ref: '#/components/schemas/ProductItem'

    FarmerListRequest:
      type: object
      properties:
        location:
          type: object
          required:
            - latitude
            - longitude
          properties:
            latitude:
              type: number
              description: 用户位置纬度
              example: 30.2741
            longitude:
              type: number
              description: 用户位置经度
              example: 120.1551
        radius:
          type: number
          description: 搜索半径（公里）
          example: 10
          default: 10
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        sortBy:
          type: string
          enum: [distance, rating, sales]
          default: distance
          description: 排序方式
      example:
        location:
          latitude: 30.2741
          longitude: 120.1551
        radius: 10
        page: 1
        pageSize: 20
        sortBy: "distance"

    FarmerListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 25
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            farmers:
              type: array
              items:
                $ref: '#/components/schemas/FarmerItem'

    FarmerItem:
      type: object
      properties:
        id:
          type: string
          description: 农户ID
          example: "farmer_123456"
        name:
          type: string
          description: 农户名称
          example: "张大叔家的农场"
        avatar:
          type: string
          description: 农户头像
          example: "https://images.example.com/farmer1.jpg"
        description:
          type: string
          description: 农户简介
          example: "主营：散养土鸡、土鸡蛋、自种蔬菜"
        location:
          type: string
          description: 农户位置
          example: "杭州市临安区"
        distance:
          type: number
          description: 距离（公里）
          example: 1.2
        rating:
          type: number
          description: 评分
          example: 4.8
        reviewCount:
          type: integer
          description: 评价数量
          example: 128
        productCount:
          type: integer
          description: 在售商品数量
          example: 15
        specialties:
          type: array
          items:
            type: string
          description: 主营产品
          example: ["散养土鸡", "土鸡蛋", "有机蔬菜"]
        isVerified:
          type: boolean
          description: 是否已认证
          example: true

    FarmerDetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          $ref: '#/components/schemas/FarmerDetail'

    FarmerDetail:
      type: object
      properties:
        id:
          type: string
          description: 农户ID
          example: "farmer_123456"
        name:
          type: string
          description: 农户名称
          example: "张大叔家的农场"
        avatar:
          type: string
          description: 农户头像
          example: "https://images.example.com/farmer1.jpg"
        description:
          type: string
          description: 农户详细介绍
          example: "从事养殖业20年，专注散养土鸡和有机蔬菜种植，坚持天然无污染的养殖方式..."
        location:
          type: string
          description: 农户位置
          example: "杭州市临安区"
        distance:
          type: number
          description: 距离（公里）
          example: 1.2
        rating:
          type: number
          description: 评分
          example: 4.8
        reviewCount:
          type: integer
          description: 评价数量
          example: 128
        totalSales:
          type: integer
          description: 总销量
          example: 2580
        joinTime:
          type: string
          format: date
          description: 加入时间
          example: "2022-03-15"
        specialties:
          type: array
          items:
            type: string
          description: 主营产品
          example: ["散养土鸡", "土鸡蛋", "有机蔬菜"]
        certifications:
          type: array
          items:
            type: string
          description: 认证信息
          example: ["有机认证", "绿色食品认证"]
        farmImages:
          type: array
          items:
            type: string
          description: 农场图片
          example: ["https://images.example.com/farm1.jpg", "https://images.example.com/farm2.jpg"]
        contactInfo:
          type: object
          properties:
            phone:
              type: string
              description: 联系电话（脱敏）
              example: "138****6789"
            wechat:
              type: string
              description: 微信号
              example: "farmer_zhang"
            address:
              type: string
              description: 详细地址
              example: "杭州市临安区天目山镇"
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductItem'
          description: 农户商品列表
        isVerified:
          type: boolean
          description: 是否已认证
          example: true

    CreateOrderRequest:
      type: object
      required:
        - items
        - deliveryAddress
        - deliveryMethod
      properties:
        items:
          type: array
          items:
            type: object
            required:
              - productId
              - quantity
            properties:
              productId:
                type: string
                description: 商品ID
                example: "product_123456"
              quantity:
                type: integer
                description: 购买数量
                example: 2
                minimum: 1
              specifications:
                type: string
                description: 规格要求
                example: "2.5-3斤/只"
          minItems: 1
        deliveryAddress:
          type: object
          required:
            - receiverName
            - receiverPhone
            - address
          properties:
            receiverName:
              type: string
              description: 收货人姓名
              example: "张三"
            receiverPhone:
              type: string
              description: 收货人电话
              example: "13800138000"
            address:
              type: string
              description: 收货地址
              example: "杭州市拱墅区春题·杭玥府5栋1单元1302"
            coordinates:
              type: object
              properties:
                latitude:
                  type: number
                  description: 纬度
                  example: 30.2741
                longitude:
                  type: number
                  description: 经度
                  example: 120.1551
        deliveryMethod:
          type: string
          description: 配送方式
          example: "同城配送"
          enum: ["自提", "同城配送", "快递配送"]
        deliveryTime:
          type: string
          description: 期望配送时间
          example: "今天下午"
        remark:
          type: string
          description: 订单备注
          example: "请在下午3点后配送"
          maxLength: 200
      example:
        items:
          - productId: "product_123456"
            quantity: 2
            specifications: "2.5-3斤/只"
        deliveryAddress:
          receiverName: "张三"
          receiverPhone: "13800138000"
          address: "杭州市拱墅区春题·杭玥府5栋1单元1302"
          coordinates:
            latitude: 30.2741
            longitude: 120.1551
        deliveryMethod: "同城配送"
        deliveryTime: "今天下午"
        remark: "请在下午3点后配送"

    CreateOrderResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "订单创建成功"
          description: 响应消息
        data:
          type: object
          properties:
            orderId:
              type: string
              description: 订单ID
              example: "order_789012"
            orderNumber:
              type: string
              description: 订单号
              example: "ORD20240115001"
            totalAmount:
              type: number
              description: 订单总金额
              example: 82.6
            status:
              type: string
              description: 订单状态
              example: "pending"
              enum: ["pending", "confirmed", "preparing", "delivering", "completed", "cancelled"]
            createTime:
              type: string
              format: date-time
              description: 创建时间
              example: "2024-01-15T10:30:00Z"
            paymentDeadline:
              type: string
              format: date-time
              description: 支付截止时间
              example: "2024-01-15T11:30:00Z"

    OrderListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 25
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            orders:
              type: array
              items:
                $ref: '#/components/schemas/OrderItem'

    OrderItem:
      type: object
      properties:
        id:
          type: string
          description: 订单ID
          example: "order_123456"
        orderNumber:
          type: string
          description: 订单号
          example: "ORD20240115001"
        status:
          type: string
          description: 订单状态
          example: "confirmed"
          enum: ["pending", "confirmed", "preparing", "delivering", "completed", "cancelled"]
        totalAmount:
          type: number
          description: 订单总金额
          example: 82.6
        itemCount:
          type: integer
          description: 商品数量
          example: 3
        farmer:
          type: object
          properties:
            id:
              type: string
              description: 农户ID
              example: "farmer_789"
            name:
              type: string
              description: 农户名称
              example: "张大叔家的农场"
            avatar:
              type: string
              description: 农户头像
              example: "https://images.example.com/farmer1.jpg"
        mainProduct:
          type: object
          properties:
            name:
              type: string
              description: 主要商品名称
              example: "散养土鸡"
            image:
              type: string
              description: 商品图片
              example: "https://images.example.com/chicken1.jpg"
        createTime:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-15T10:30:00Z"
        deliveryMethod:
          type: string
          description: 配送方式
          example: "同城配送"

    OrderDetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          $ref: '#/components/schemas/OrderDetail'

    OrderDetail:
      type: object
      properties:
        id:
          type: string
          description: 订单ID
          example: "order_123456"
        orderNumber:
          type: string
          description: 订单号
          example: "ORD20240115001"
        status:
          type: string
          description: 订单状态
          example: "confirmed"
          enum: ["pending", "confirmed", "preparing", "delivering", "completed", "cancelled"]
        items:
          type: array
          items:
            type: object
            properties:
              productId:
                type: string
                description: 商品ID
                example: "product_123456"
              productName:
                type: string
                description: 商品名称
                example: "散养土鸡"
              productImage:
                type: string
                description: 商品图片
                example: "https://images.example.com/chicken1.jpg"
              price:
                type: number
                description: 单价
                example: 38.8
              quantity:
                type: integer
                description: 数量
                example: 2
              unit:
                type: string
                description: 单位
                example: "只"
              subtotal:
                type: number
                description: 小计
                example: 77.6
              specifications:
                type: string
                description: 规格
                example: "2.5-3斤/只"
        farmer:
          type: object
          properties:
            id:
              type: string
              description: 农户ID
              example: "farmer_789"
            name:
              type: string
              description: 农户名称
              example: "张大叔家的农场"
            avatar:
              type: string
              description: 农户头像
              example: "https://images.example.com/farmer1.jpg"
            phone:
              type: string
              description: 联系电话（脱敏）
              example: "138****6789"
        deliveryAddress:
          type: object
          properties:
            receiverName:
              type: string
              description: 收货人姓名
              example: "张三"
            receiverPhone:
              type: string
              description: 收货人电话
              example: "13800138000"
            address:
              type: string
              description: 收货地址
              example: "杭州市拱墅区春题·杭玥府5栋1单元1302"
        deliveryMethod:
          type: string
          description: 配送方式
          example: "同城配送"
        deliveryFee:
          type: number
          description: 配送费
          example: 5
        totalAmount:
          type: number
          description: 订单总金额
          example: 82.6
        createTime:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-15T10:30:00Z"
        confirmTime:
          type: string
          format: date-time
          description: 确认时间
          example: "2024-01-15T10:35:00Z"
          nullable: true
        deliveryTime:
          type: string
          format: date-time
          description: 配送时间
          example: "2024-01-15T14:00:00Z"
          nullable: true
        remark:
          type: string
          description: 订单备注
          example: "请在下午3点后配送"

    UploadResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "上传成功"
          description: 响应消息
        data:
          type: object
          properties:
            images:
              type: array
              items:
                type: object
                properties:
                  url:
                    type: string
                    description: 图片URL
                    example: "https://images.example.com/product1_1.jpg"
                  filename:
                    type: string
                    description: 文件名
                    example: "product1_1.jpg"
                  size:
                    type: integer
                    description: 文件大小（字节）
                    example: 1024000
                  isMain:
                    type: boolean
                    description: 是否为主图
                    example: true

    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "productId字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"

tags:
  - name: 商品管理
    description: 农产品的基本管理功能，包括列表查询、详情获取、发布等
  - name: 商品搜索
    description: 农产品搜索相关功能
  - name: 商品分类
    description: 农产品分类管理功能
  - name: 商品推荐
    description: 热卖商品推荐功能
  - name: 农户管理
    description: 农户信息管理功能
  - name: 订单管理
    description: 订单创建、查询、状态管理等功能
  - name: 文件上传
    description: 商品图片等文件上传功能
