<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 商品详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }

        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-large);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            margin: 12px 16px;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--ios-label);
        }
        
        /* iOS底部操作栏 */
        .ios-action-bar {
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
            border-top: 0.5px solid var(--ios-separator);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 30;
        }

        /* iOS主按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 12px 20px;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-primary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.8);
        }

        /* iOS次要按钮 */
        .ios-secondary-button {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--ios-blue);
            border-radius: var(--ios-corner-radius-medium);
            font-size: 15px;
            font-weight: 600;
            padding: 10px 16px;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-secondary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.15);
        }

        /* iOS选择项 */
        .ios-option {
            border-radius: var(--ios-corner-radius-xl);
            font-size: 15px;
            padding: 8px 14px;
            transition: all 0.2s ease;
            border: 1px solid var(--ios-separator);
        }
        
        .ios-option.selected {
            background-color: rgba(0, 122, 255, 0.1);
            border-color: var(--ios-blue);
            color: var(--ios-blue);
        }
        
        /* 数量选择控件 */
        .ios-quantity-control {
            display: flex;
            align-items: center;
        }
        
        .ios-quantity-button {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--ios-separator);
            transition: all 0.2s ease;
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }

        /* iOS图片计数器 */
        .ios-image-counter {
            background-color: rgba(0,0,0,0.5);
            padding: 4px 10px;
            border-radius: 13px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }

        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: var(--ios-separator);
            width: 100%;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
        
        .category-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">商品详情</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="ios-nav-title">商品详情</div>
                <button class="ios-button ios-haptic text-[var(--ios-secondaryLabel)]">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
            
            <div class="screen-content pb-24">
                <!-- 轮播图 -->
                <div class="relative ios-fade-in ios-fade-in-delay-1">
                    <div class="w-full h-80 bg-[var(--ios-secondarySystemBackground)]">
                        <img src="https://images.unsplash.com/photo-1518492104633-130d0cc84637" class="w-full h-full object-cover">
                    </div>
                    <div class="absolute bottom-3 right-3 ios-image-counter">
                        1/3
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-2">
                    <div class="p-4 space-y-5">
                        <!-- 商品标题和收藏 -->
                        <div class="flex justify-between items-start gap-3">
                            <div class="space-y-2 flex-1 min-w-0">
                                <h2 class="text-[20px] font-semibold leading-tight truncate">散养土鸡</h2>
                                <div class="flex items-baseline flex-wrap gap-x-1">
                                    <span class="text-[20px] font-semibold text-[var(--ios-red)]">¥38.8</span>
                                    <span class="text-[13px] text-[var(--ios-red)]">/只</span>
                                    <span class="text-[12px] text-[var(--ios-secondaryLabel)] line-through">¥48.8</span>
                                </div>
                            </div>
                            <button class="ios-button ios-haptic p-2 shrink-0">
                                <i class="far fa-heart text-[var(--ios-secondaryLabel)] text-lg"></i>
                            </button>
                        </div>

                        <!-- 评分和销量 -->
                        <div class="flex items-center divide-x divide-[var(--ios-separator)]">
                            <div class="flex items-center pr-3">
                                <div class="flex space-x-[1px]">
                                    <i class="fas fa-star text-yellow-400 text-[13px]"></i>
                                    <i class="fas fa-star text-yellow-400 text-[13px]"></i>
                                    <i class="fas fa-star text-yellow-400 text-[13px]"></i>
                                    <i class="fas fa-star text-yellow-400 text-[13px]"></i>
                                    <i class="fas fa-star-half-alt text-yellow-400 text-[13px]"></i>
                                </div>
                                <span class="text-[13px] ml-1.5 font-medium">4.8</span>
                            </div>
                            <span class="text-[13px] px-3 text-[var(--ios-secondaryLabel)]">已售 92只</span>
                            <span class="text-[13px] pl-3 text-[var(--ios-secondaryLabel)]">库存 28只</span>
                        </div>

                        <!-- 基本信息 -->
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-[var(--ios-secondarySystemBackground)] flex items-center justify-center">
                                    <i class="fas fa-weight text-[var(--ios-secondaryLabel)] text-base"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-[15px] font-medium">约2-2.5斤/只</p>
                                    <p class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">净重</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-[var(--ios-secondarySystemBackground)] flex items-center justify-center">
                                    <i class="fas fa-truck text-[var(--ios-secondaryLabel)] text-base"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-[15px] font-medium">当日现杀现送</p>
                                    <p class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">配送</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-[var(--ios-secondarySystemBackground)] flex items-center justify-center">
                                    <i class="fas fa-map-marker-alt text-[var(--ios-secondaryLabel)] text-base"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-[15px] font-medium">杭州市临平区</p>
                                    <p class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">产地</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-[var(--ios-secondarySystemBackground)] flex items-center justify-center">
                                    <i class="fas fa-calendar-alt text-[var(--ios-secondaryLabel)] text-base"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-[15px] font-medium">每周二、四、六</p>
                                    <p class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">售卖时间</p>
                                </div>
                            </div>
                        </div>

                        <!-- 选择规格 -->
                        <div class="space-y-3">
                            <h3 class="text-[17px] font-semibold">选择规格</h3>
                            <div class="flex flex-wrap gap-2">
                                <button class="ios-option selected ios-button ios-haptic text-[15px] py-2.5 px-4">
                                    整鸡（约2斤）
                                </button>
                                <button class="ios-option ios-button ios-haptic text-[15px] py-2.5 px-4">
                                    分割鸡（约2斤）
                                </button>
                                <button class="ios-option ios-button ios-haptic text-[15px] py-2.5 px-4">
                                    小公鸡（约1.5斤）
                                </button>
                                <button class="ios-option ios-button ios-haptic text-[15px] py-2.5 px-4">
                                    老母鸡（约3斤）
                                </button>
                            </div>
                        </div>

                        <!-- 数量选择 -->
                        <div class="flex justify-between items-center">
                            <span class="text-[17px] font-semibold">购买数量</span>
                            <div class="ios-quantity-control">
                                <button class="ios-quantity-button ios-button ios-haptic w-8 h-8">
                                    <i class="fas fa-minus text-[var(--ios-secondaryLabel)] text-[13px]"></i>
                                </button>
                                <span class="mx-5 text-center w-8 text-[17px]">1</span>
                                <button class="ios-quantity-button ios-button ios-haptic w-8 h-8">
                                    <i class="fas fa-plus text-[var(--ios-blue)] text-[13px]"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 农户信息 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-3">
                    <div class="p-4">
                        <h3 class="text-[15px] font-medium mb-3">农户信息</h3>
                        <div class="flex items-center justify-between gap-3">
                            <div class="flex items-center min-w-0">
                                <img src="https://images.unsplash.com/photo-1595152772835-219674b2a8a6" class="w-11 h-11 rounded-full shrink-0 mr-2.5 object-cover">
                                <div class="min-w-0">
                                    <h4 class="text-[15px] font-medium truncate">张大叔家的农场</h4>
                                    <p class="text-[12px] text-[var(--ios-secondaryLabel)] mt-0.5 truncate">已认证 · 临平区农户</p>
                                </div>
                            </div>
                            <button class="ios-secondary-button ios-button ios-haptic shrink-0 text-[13px] py-1.5 px-3" onclick="window.location.href='farmer-detail.html?id=1'">
                                查看店铺
                            </button>
                        </div>
                        <div class="flex justify-between mt-3">
                            <div class="text-[12px] text-[var(--ios-secondaryLabel)]">
                                <span class="font-medium text-[var(--ios-green)]">98.7%</span>
                                <span class="ml-1">好评率</span>
                            </div>
                            <div class="text-[12px] text-[var(--ios-secondaryLabel)]">
                                <span class="font-medium text-[var(--ios-green)]">156</span>
                                <span class="ml-1">成交量</span>
                            </div>
                            <div class="text-[12px] text-[var(--ios-secondaryLabel)]">
                                <span class="font-medium text-[var(--ios-green)]">4小时</span>
                                <span class="ml-1">平均送达</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品详情 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-4">
                    <div class="p-4">
                        <h3 class="font-medium text-base mb-2">商品详情</h3>
                        <div class="mt-3 text-sm text-[var(--ios-secondaryLabel)] space-y-3">
                            <p>【农场简介】我家农场位于临平区农村地区，远离城市污染，环境清新自然。</p>
                            <p>【散养环境】土鸡完全放养在自然环境中，采用传统方式喂养，不使用任何生长激素，成长周期在180天以上。</p>
                            <p>【品质保证】土鸡肉质紧实有嚼劲，鸡汤浓郁，营养丰富，香味十足，是城里很难吃到的原生态美食。</p>
                            <p>【配送说明】提前一天预订，次日现杀现送，保证新鲜。配送范围限临平区及周边5公里范围内，其他区域需加收运费。</p>
                        </div>

                        <div class="mt-4 space-y-2">
                            <img src="https://images.unsplash.com/photo-1587486913049-53fc88980cfc" class="w-full rounded-lg">
                            <img src="https://images.unsplash.com/photo-1501427288714-e990e6aad392" class="w-full rounded-lg">
                        </div>
                    </div>
                </div>

                <!-- 用户评价 -->
                <div class="ios-card ios-fade-in ios-fade-in-delay-4 mb-24">
                    <div class="p-4">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium text-base">用户评价</h3>
                            <a href="reviews.html?id=1" class="ios-button ios-haptic text-sm text-[var(--ios-blue)] flex items-center">
                                查看全部
                                <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>

                        <div class="mt-3 space-y-4">
                            <div class="pb-3 border-b border-[var(--ios-separator)]">
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956" class="w-8 h-8 rounded-full mr-2 object-cover">
                                    <span>李**</span>
                                    <div class="flex ml-3">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-sm mt-2">真的是散养土鸡，肉质很好，熬汤很香，第三次买了，味道一直很稳定，以后还会继续购买。</p>
                                <div class="flex mt-2">
                                    <img src="https://images.unsplash.com/photo-1562967915-92ae0c320a01" class="w-16 h-16 rounded mr-2 object-cover">
                                    <img src="https://images.unsplash.com/photo-1527477396000-e27163b481c2" class="w-16 h-16 rounded object-cover">
                                </div>
                                <div class="text-xs text-[var(--ios-secondaryLabel)] mt-2">2025-03-15</div>
                            </div>

                            <div>
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1595152772835-219674b2a8a6" class="w-8 h-8 rounded-full mr-2 object-cover">
                                    <span>王**</span>
                                    <div class="flex ml-3">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-gray-300 text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-sm mt-2">送货很及时，鸡肉很新鲜，确实比超市买的味道好很多，就是价格稍贵一些。</p>
                                <div class="text-xs text-[var(--ios-secondaryLabel)] mt-2">2025-03-10</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 推荐商品 -->
                <div class="mt-2 bg-white p-4">
                    <h3 class="font-medium text-base mb-3">相关推荐</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-gray-50 rounded-lg p-2" onclick="window.location.href='product-detail.html?id=3'">
                            <img src="https://images.unsplash.com/photo-1598103442097-8b74394b95c6" class="w-full aspect-square object-cover rounded-lg mb-2">
                            <h4 class="text-sm font-medium truncate">土鸡蛋</h4>
                            <div class="flex justify-between items-center mt-1">
                                <p class="text-sm text-red-500 font-medium">¥3.5/个</p>
                                <span class="text-xs text-gray-500">已售108件</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-2" onclick="window.location.href='product-detail.html?id=4'">
                            <img src="https://images.unsplash.com/photo-1598788841836-739cab3b3f95" class="w-full aspect-square object-cover rounded-lg mb-2">
                            <h4 class="text-sm font-medium truncate">自种有机蔬菜</h4>
                            <div class="flex justify-between items-center mt-1">
                                <p class="text-sm text-red-500 font-medium">¥15.8/份</p>
                                <span class="text-xs text-gray-500">已售92件</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div class="ios-action-bar">
                <div class="flex items-center justify-between space-x-4 px-2">
                    <div class="flex space-x-6">
                        <button class="ios-button ios-haptic flex flex-col items-center py-1">
                            <i class="fas fa-store text-[var(--ios-secondaryLabel)] text-lg"></i>
                            <span class="text-[10px] text-[var(--ios-secondaryLabel)] mt-1.5">店铺</span>
                        </button>
                        <button class="ios-button ios-haptic flex flex-col items-center py-1">
                            <i class="fas fa-headset text-[var(--ios-secondaryLabel)] text-lg"></i>
                            <span class="text-[10px] text-[var(--ios-secondaryLabel)] mt-1.5">客服</span>
                        </button>
                    </div>
                    <div class="flex space-x-3 flex-1 ml-2">
                        <button class="ios-secondary-button ios-button ios-haptic flex-1 py-3 text-[15px]">加入购物车</button>
                        <button class="ios-primary-button ios-button ios-haptic flex-1 py-3 text-[15px]">立即购买</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 规格选择
            const optionButtons = document.querySelectorAll('.ios-option');
            optionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 添加强一点的触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([5, 15]);
                    }
                    
                    // 清除所有选中状态
                    optionButtons.forEach(btn => {
                        btn.classList.remove('selected');
                    });
                    
                    // 设置当前按钮为选中状态
                    this.classList.add('selected');
                });
            });
            
            // 数量控制
            const minusButton = document.querySelector('.ios-quantity-button:first-child');
            const plusButton = document.querySelector('.ios-quantity-button:last-child');
            const quantityDisplay = document.querySelector('.ios-quantity-control span');
            
            minusButton.addEventListener('click', function() {
                let currentValue = parseInt(quantityDisplay.textContent);
                if (currentValue > 1) {
                    quantityDisplay.textContent = currentValue - 1;
                    
                    // 添加触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate(8);
                    }
                }
            });
            
            plusButton.addEventListener('click', function() {
                let currentValue = parseInt(quantityDisplay.textContent);
                quantityDisplay.textContent = currentValue + 1;
                
                // 添加触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate(8);
                }
            });
            
            // 收藏按钮效果
            const favoriteButton = document.querySelector('.far.fa-heart').parentElement;
            favoriteButton.addEventListener('click', function() {
                const icon = this.querySelector('i');
                
                // 添加触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate([10, 20]);
                }
                
                // 切换收藏状态
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    icon.classList.add('text-[var(--ios-red)]');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.remove('text-[var(--ios-red)]');
                    icon.classList.add('far');
                }
            });
            
            // 主操作按钮触感反馈
            const primaryButtons = document.querySelectorAll('.ios-primary-button, .ios-secondary-button');
            primaryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 添加更强的触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([10, 30, 10]);
                    }
                });
            });
        });
    </script>
</body>
</html> 