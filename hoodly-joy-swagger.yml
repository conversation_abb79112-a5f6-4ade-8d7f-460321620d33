openapi: 3.0.3
info:
  title: 乐享友邻 - 综合平台API
  description: |
    乐享友邻邻里社区综合平台后端接口文档

    ## 响应格式说明

    ### 成功响应格式
    ```json
    {
      "success": true,
      "data": {
        // 具体的响应数据
      },
      "traceId": "trace_123456789"
    }
    ```

    ### 错误响应格式
    ```json
    {
      "success": false,
      "data": null,
      "traceId": "trace_123456789",
      "err": {
        "code": "ERROR_CODE",
        "msg": "执行发生错误",
        "suggest": "错误建议信息或null"
      }
    }
    ```

    ## 功能模块
    - 用户认证：登录、注册、密码管理、第三方登录
    - 社区管理：小区选择、小区注册、小区信息管理
    - 首页导航：首页推荐、发现页面、消息管理、通知管理
    - 二手闲置：二手商品交易、发布管理
    - 房屋租赁：房源信息、租赁管理
    - 停车位：停车位租售、管理
    - 邻里集市：农产品交易、农户管理、订单处理
    - 邻里拼单：拼单活动管理、参与拼单
    - 用户中心：个人信息、收藏、历史记录、设置
    - 公共服务：文件上传、地理位置等
  version: 1.0.0
  contact:
    name: 乐享友邻技术团队
    email: <EMAIL>

servers:
  - url: https://dev-api.hoodly-joy.com/api
    description: 开发环境
  - url: https://test-api.hoodly-joy.com/api
    description: 测试环境
  - url: https://staging-api.hoodly-joy.com/api
    description: 预发演示环境
  - url: https://api.hoodly-joy.com/api
    description: 生产环境

security:
  - BearerAuth: []

paths:
  # ==================== 用户认证模块 ====================
  /auth/login:
    post:
      tags:
        - 用户认证
      summary: 用户登录
      description: 使用手机号和密码进行用户登录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "请求参数验证失败"
                  suggest: "请检查手机号和密码格式是否正确"
        '401':
          description: 用户名或密码错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "用户名或密码错误"
                  suggest: "请检查登录凭据或重置密码"
        '423':
          description: 账户被锁定
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "ACCOUNT_LOCKED"
                  msg: "账户已被锁定"
                  suggest: "请联系客服解锁账户"

  /auth/register:
    post:
      tags:
        - 用户认证
      summary: 用户注册
      description: 使用手机号和验证码进行用户注册
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "注册参数验证失败"
                  suggest: "请检查手机号、密码格式是否正确"
        '409':
          description: 手机号已注册
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "USER_ALREADY_EXISTS"
                  msg: "用户已存在"
                  suggest: "请使用其他手机号注册或直接登录"

  /auth/sms/send:
    post:
      tags:
        - 用户认证
      summary: 发送短信验证码
      description: 向指定手机号发送短信验证码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendSmsRequest'
      responses:
        '200':
          description: 验证码发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendSmsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "短信发送参数错误"
                  suggest: "请检查手机号格式是否正确"
        '429':
          description: 发送频率过高
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "RATE_LIMIT_EXCEEDED"
                  msg: "发送频率过高"
                  suggest: "请稍后再试，每分钟最多发送1次"

  /auth/sms/verify:
    post:
      tags:
        - 用户认证
      summary: 验证短信验证码
      description: 验证手机号和短信验证码的有效性
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifySmsRequest'
      responses:
        '200':
          description: 验证成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifySmsResponse'
        '400':
          description: 请求参数错误或验证码错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "验证码错误或已过期"
                  suggest: "请检查验证码是否正确或重新获取"
        '410':
          description: 验证码已过期
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "SMS_CODE_EXPIRED"
                  msg: "验证码已过期"
                  suggest: "请重新获取验证码"

  /auth/password/reset:
    post:
      tags:
        - 用户认证
      summary: 重置密码
      description: 通过手机号和验证码重置用户密码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: 密码重置成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "密码重置参数错误"
                  suggest: "请检查手机号、验证码和新密码格式"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "USER_NOT_FOUND"
                  msg: "用户不存在"
                  suggest: "请检查手机号是否正确"

  /auth/password/change:
    put:
      tags:
        - 用户认证
      summary: 修改密码
      description: 用户修改登录密码
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200':
          description: 密码修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "密码修改参数错误"
                  suggest: "请检查密码格式是否正确"
        '401':
          description: 原密码错误或未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "原密码错误"
                  suggest: "请检查原密码是否正确"

  /auth/oauth/{provider}:
    get:
      tags:
        - 用户认证
      summary: 第三方登录授权
      description: 获取第三方登录授权URL
      parameters:
        - name: provider
          in: path
          required: true
          description: 第三方登录提供商
          schema:
            type: string
            enum: [wechat, qq, alipay]
            example: "wechat"
        - name: redirect_uri
          in: query
          description: 授权成功后的回调地址
          schema:
            type: string
            example: "https://app.hoodly-joy.com/auth/callback"
      responses:
        '200':
          description: 成功获取授权URL
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthUrlResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "第三方登录参数错误"
                  suggest: "请检查登录提供商和回调地址"

  /auth/oauth/{provider}/callback:
    post:
      tags:
        - 用户认证
      summary: 第三方登录回调
      description: 处理第三方登录授权回调
      parameters:
        - name: provider
          in: path
          required: true
          description: 第三方登录提供商
          schema:
            type: string
            enum: [wechat, qq, alipay]
            example: "wechat"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthCallbackRequest'
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "第三方登录回调参数错误"
                  suggest: "请检查授权码和状态参数"
        '401':
          description: 授权失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "OAUTH_AUTH_FAILED"
                  msg: "第三方登录授权失败"
                  suggest: "请重新进行第三方登录授权"

  /auth/logout:
    post:
      tags:
        - 用户认证
      summary: 用户登出
      description: 用户登出，清除登录状态
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"

  /auth/refresh:
    post:
      tags:
        - 用户认证
      summary: 刷新访问令牌
      description: 使用刷新令牌获取新的访问令牌
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: 令牌刷新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "刷新令牌参数错误"
                  suggest: "请提供有效的刷新令牌"
        '401':
          description: 刷新令牌无效或已过期
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "TOKEN_EXPIRED"
                  msg: "刷新令牌无效或已过期"
                  suggest: "请重新登录"

  # ==================== 社区管理模块 ====================
  /communities/list:
    post:
      tags:
        - 社区管理
      summary: 获取小区列表
      description: 根据位置和条件获取小区列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommunityListRequest'
      responses:
        '200':
          description: 成功获取小区列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunityListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "小区列表参数验证失败"
                  suggest: "请检查分页参数和筛选条件"

  /communities/{communityId}:
    get:
      tags:
        - 社区管理
      summary: 获取小区详情
      description: 根据小区ID获取小区详细信息
      parameters:
        - name: communityId
          in: path
          required: true
          description: 小区唯一标识ID
          schema:
            type: string
            example: "community_123456"
      responses:
        '200':
          description: 成功获取小区详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunityDetailResponse'
        '404':
          description: 小区不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "COMMUNITY_NOT_FOUND"
                  msg: "请求的小区不存在"
                  suggest: "请检查小区ID是否正确"

  /communities/join:
    post:
      tags:
        - 社区管理
      summary: 加入小区
      description: 用户申请加入指定小区
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JoinCommunityRequest'
      responses:
        '201':
          description: 申请提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JoinCommunityResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "加入小区参数验证失败"
                  suggest: "请检查小区ID和申请信息"

  /communities/{communityId}/bind:
    post:
      tags:
        - 社区管理
      summary: 绑定小区
      description: 用户绑定到指定小区
      security:
        - BearerAuth: []
      parameters:
        - name: communityId
          in: path
          required: true
          description: 小区唯一标识ID
          schema:
            type: string
            example: "community_123456"
      responses:
        '200':
          description: 绑定成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BindCommunityResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "绑定参数错误"
                  suggest: "请检查小区ID是否正确"
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"
        '409':
          description: 用户已绑定其他小区
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "ALREADY_BOUND"
                  msg: "用户已绑定其他小区"
                  suggest: "请先解绑当前小区或使用切换小区功能"

  /communities/register:
    post:
      tags:
        - 社区管理
      summary: 申请注册小区
      description: 用户申请注册新的小区
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterCommunityRequest'
      responses:
        '201':
          description: 申请提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterCommunityResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "注册申请参数错误"
                  suggest: "请检查必填字段是否完整"
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"

  /communities/{communityId}/switch:
    post:
      tags:
        - 社区管理
      summary: 切换小区
      description: 用户切换到指定小区
      security:
        - BearerAuth: []
      parameters:
        - name: communityId
          in: path
          required: true
          description: 要切换到的小区唯一标识ID
          schema:
            type: string
            example: "community_789012"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: 切换原因（可选）
                  example: "搬家到新小区"
                  maxLength: 200
      responses:
        '200':
          description: 切换成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwitchCommunityResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "切换小区参数错误"
                  suggest: "请检查小区ID是否正确"
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"
        '403':
          description: 无权限切换到该小区
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PERMISSION_DENIED"
                  msg: "无权限切换到该小区"
                  suggest: "请先绑定该小区或联系管理员"

  # ==================== 首页导航模块 ====================
  /home/<USER>
    get:
      tags:
        - 首页导航
      summary: 获取首页仪表盘数据
      description: 获取首页展示的各模块推荐内容和统计信息
      responses:
        '200':
          description: 成功获取首页数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HomeDashboardResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"

  /search/global:
    post:
      tags:
        - 首页导航
      summary: 全局搜索
      description: 在所有模块中搜索相关内容
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GlobalSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalSearchResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "搜索参数验证失败"
                  suggest: "请检查搜索关键词和筛选条件"

  # ==================== 二手闲置模块 ====================
  /second-hand/products/list:
    post:
      tags:
        - 二手闲置
      summary: 获取二手商品列表
      description: 根据分类、价格等条件获取二手商品列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecondHandListRequest'
      responses:
        '200':
          description: 成功获取商品列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecondHandListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "二手商品列表参数错误"
                  suggest: "请检查分页参数和筛选条件"

  /second-hand/products/{productId}:
    get:
      tags:
        - 二手闲置
      summary: 获取二手商品详情
      description: 根据商品ID获取二手商品的详细信息
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 成功获取商品详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecondHandDetailResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PRODUCT_NOT_FOUND"
                  msg: "请求的商品不存在"
                  suggest: "请检查商品ID是否正确"

    put:
      tags:
        - 二手闲置
      summary: 更新二手商品
      description: 更新已发布的二手商品信息
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSecondHandRequest'
      responses:
        '200':
          description: 商品更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "商品更新参数错误"
                  suggest: "请检查商品信息是否完整"
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PRODUCT_NOT_FOUND"
                  msg: "请求的商品不存在"
                  suggest: "请检查商品ID是否正确"

    delete:
      tags:
        - 二手闲置
      summary: 删除二手商品
      description: 删除已发布的二手商品
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 商品删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PRODUCT_NOT_FOUND"
                  msg: "请求的商品不存在"
                  suggest: "请检查商品ID是否正确"

  /second-hand/products:
    post:
      tags:
        - 二手闲置
      summary: 发布二手商品
      description: 用户发布新的二手商品
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishSecondHandRequest'
      responses:
        '201':
          description: 商品发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishSecondHandResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "商品发布参数错误"
                  suggest: "请检查商品标题、价格、描述等必填字段"



  /second-hand/products/search:
    post:
      tags:
        - 二手闲置
      summary: 搜索二手商品
      description: 根据关键词搜索二手商品
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecondHandSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecondHandListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "二手商品列表参数错误"
                  suggest: "请检查分页参数和筛选条件"

  /second-hand/categories:
    get:
      tags:
        - 二手闲置
      summary: 获取商品分类列表
      description: 获取所有二手商品分类信息
      responses:
        '200':
          description: 成功获取分类列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecondHandCategoriesResponse'

  /second-hand/products/{productId}/favorite:
    post:
      tags:
        - 二手闲置
      summary: 收藏商品
      description: 收藏指定的二手商品
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PRODUCT_NOT_FOUND"
                  msg: "请求的商品不存在"
                  suggest: "请检查商品ID是否正确"

    delete:
      tags:
        - 二手闲置
      summary: 取消收藏商品
      description: 取消收藏指定的二手商品
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PRODUCT_NOT_FOUND"
                  msg: "请求的商品不存在"
                  suggest: "请检查商品ID是否正确"

  # ==================== 房屋租赁模块 ====================
  /housing/properties/list:
    post:
      tags:
        - 房屋租赁
      summary: 获取房源列表
      description: 根据位置、价格等条件获取房源列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HousingListRequest'
      responses:
        '200':
          description: 成功获取房源列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HousingListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "房源列表参数错误"
                  suggest: "请检查分页参数和筛选条件"

  /housing/properties/{propertyId}:
    get:
      tags:
        - 房屋租赁
      summary: 获取房源详情
      description: 根据房源ID获取房源的详细信息
      parameters:
        - name: propertyId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "property_123456"
      responses:
        '200':
          description: 成功获取房源详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HousingDetailResponse'
        '404':
          description: 房源不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PROPERTY_NOT_FOUND"
                  msg: "请求的房源不存在"
                  suggest: "请检查房源ID是否正确"

    put:
      tags:
        - 房屋租赁
      summary: 更新房源信息
      description: 更新已发布的房源信息
      parameters:
        - name: propertyId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "property_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateHousingRequest'
      responses:
        '200':
          description: 房源更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "房源更新参数错误"
                  suggest: "请检查房源信息是否完整"
        '404':
          description: 房源不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PROPERTY_NOT_FOUND"
                  msg: "请求的房源不存在"
                  suggest: "请检查房源ID是否正确"

    delete:
      tags:
        - 房屋租赁
      summary: 删除房源
      description: 删除已发布的房源
      parameters:
        - name: propertyId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "property_123456"
      responses:
        '200':
          description: 房源删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 房源不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PROPERTY_NOT_FOUND"
                  msg: "请求的房源不存在"
                  suggest: "请检查房源ID是否正确"

  /housing/properties:
    post:
      tags:
        - 房屋租赁
      summary: 发布房源
      description: 用户发布新的房源信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishHousingRequest'
      responses:
        '201':
          description: 房源发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishHousingResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "房源发布参数错误"
                  suggest: "请检查房源信息是否完整"



  /housing/properties/search:
    post:
      tags:
        - 房屋租赁
      summary: 搜索房源
      description: 根据关键词搜索房源
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HousingSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HousingListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "房源搜索参数错误"
                  suggest: "请检查搜索条件和分页参数"

  /housing/properties/{propertyId}/favorite:
    post:
      tags:
        - 房屋租赁
      summary: 收藏房源
      description: 收藏指定的房源
      parameters:
        - name: propertyId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "property_123456"
      responses:
        '200':
          description: 收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 房源不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PROPERTY_NOT_FOUND"
                  msg: "请求的房源不存在"
                  suggest: "请检查房源ID是否正确"

    delete:
      tags:
        - 房屋租赁
      summary: 取消收藏房源
      description: 取消收藏指定的房源
      parameters:
        - name: propertyId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "property_123456"
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 房源不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PROPERTY_NOT_FOUND"
                  msg: "请求的房源不存在"
                  suggest: "请检查房源ID是否正确"

  # ==================== 停车位模块 ====================
  /parking/spaces/list:
    post:
      tags:
        - 停车位
      summary: 获取停车位列表
      description: 根据位置、价格等条件获取停车位列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParkingListRequest'
      responses:
        '200':
          description: 成功获取停车位列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "分页参数错误"
                  suggest: "pageNo必须大于0，pageSize必须在1-100之间"

  /parking/spaces/{spaceId}:
    get:
      tags:
        - 停车位
      summary: 获取停车位详情
      description: 根据停车位ID获取停车位的详细信息
      parameters:
        - name: spaceId
          in: path
          required: true
          description: 停车位唯一标识ID
          schema:
            type: string
            example: "space_123456"
      responses:
        '200':
          description: 成功获取停车位详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingDetailResponse'
        '404':
          description: 停车位不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "PARKING_NOT_FOUND"
                  msg: "请求的停车位不存在"
                  suggest: "请检查停车位ID是否正确"

  /parking/spaces:
    post:
      tags:
        - 停车位
      summary: 发布停车位
      description: 用户发布新的停车位信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishParkingRequest'
      responses:
        '201':
          description: 停车位发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishParkingResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "停车位发布参数错误"
                  suggest: "请检查停车位信息是否完整"

    put:
      tags:
        - 停车位
      summary: 更新停车位信息
      description: 更新已发布的停车位信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateParkingRequest'
      responses:
        '200':
          description: 停车位更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "VALIDATION_ERROR"
                  msg: "停车位更新参数错误"
                  suggest: "请检查停车位信息是否完整"

  /parking/spaces/{spaceId}/favorite:
    post:
      tags:
        - 停车位
      summary: 收藏停车位
      description: 收藏指定的停车位
      parameters:
        - name: spaceId
          in: path
          required: true
          description: 停车位唯一标识ID
          schema:
            type: string
            example: "space_123456"
      responses:
        '200':
          description: 收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 停车位不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

    delete:
      tags:
        - 停车位
      summary: 取消收藏停车位
      description: 取消收藏指定的停车位
      parameters:
        - name: spaceId
          in: path
          required: true
          description: 停车位唯一标识ID
          schema:
            type: string
            example: "space_123456"
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 停车位不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

  # ==================== 邻里集市模块 ====================
  /market/products/list:
    post:
      tags:
        - 邻里集市
      summary: 获取农产品列表
      description: 根据分类、位置等条件获取农产品列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarketProductListRequest'
      responses:
        '200':
          description: 成功获取商品列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketProductListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

  /market/products/{productId}:
    get:
      tags:
        - 邻里集市
      summary: 获取农产品详情
      description: 根据商品ID获取农产品的详细信息
      parameters:
        - name: productId
          in: path
          required: true
          description: 商品唯一标识ID
          schema:
            type: string
            example: "product_123456"
      responses:
        '200':
          description: 成功获取商品详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketProductDetailResponse'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /market/orders:
    post:
      tags:
        - 邻里集市
      summary: 创建订单
      description: 创建农产品订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMarketOrderRequest'
      responses:
        '201':
          description: 订单创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateMarketOrderResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /market/products:
    post:
      tags:
        - 邻里集市
      summary: 发布农产品
      description: 农户发布新的农产品
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishMarketProductRequest'
      responses:
        '201':
          description: 农产品发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishMarketProductResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /market/products/search:
    post:
      tags:
        - 邻里集市
      summary: 搜索农产品
      description: 根据关键词搜索农产品
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarketProductSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketProductListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /market/categories:
    get:
      tags:
        - 邻里集市
      summary: 获取农产品分类列表
      description: 获取所有农产品分类信息
      responses:
        '200':
          description: 成功获取分类列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketCategoriesResponse'

  /market/farmers/list:
    post:
      tags:
        - 邻里集市
      summary: 获取农户列表
      description: 根据位置等条件获取农户列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FarmerListRequest'
      responses:
        '200':
          description: 成功获取农户列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FarmerListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

  /market/farmers/{farmerId}:
    get:
      tags:
        - 邻里集市
      summary: 获取农户详情
      description: 根据农户ID获取农户的详细信息
      parameters:
        - name: farmerId
          in: path
          required: true
          description: 农户唯一标识ID
          schema:
            type: string
            example: "farmer_123456"
      responses:
        '200':
          description: 成功获取农户详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FarmerDetailResponse'
        '404':
          description: 农户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /market/orders/list:
    post:
      tags:
        - 邻里集市
      summary: 获取订单列表
      description: 获取用户的农产品订单列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarketOrderListRequest'
      responses:
        '200':
          description: 成功获取订单列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketOrderListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /market/orders/{orderId}:
    get:
      tags:
        - 邻里集市
      summary: 获取订单详情
      description: 根据订单ID获取订单的详细信息
      parameters:
        - name: orderId
          in: path
          required: true
          description: 订单唯一标识ID
          schema:
            type: string
            example: "order_123456"
      responses:
        '200':
          description: 成功获取订单详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketOrderDetailResponse'
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /market/orders/{orderId}/cancel:
    post:
      tags:
        - 邻里集市
      summary: 取消订单
      description: 取消指定的农产品订单
      parameters:
        - name: orderId
          in: path
          required: true
          description: 订单唯一标识ID
          schema:
            type: string
            example: "order_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: 取消原因
                  example: "临时有事无法收货"
      responses:
        '200':
          description: 订单取消成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误或订单无法取消
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

  # ==================== 邻里拼单模块 ====================
  /group-buy/activities/list:
    post:
      tags:
        - 邻里拼单
      summary: 获取拼单活动列表
      description: 根据分类、状态等条件获取拼单活动列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupBuyListRequest'
      responses:
        '200':
          description: 成功获取拼单活动列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /group-buy/activities/{activityId}:
    get:
      tags:
        - 邻里拼单
      summary: 获取拼单活动详情
      description: 根据活动ID获取拼单活动的详细信息
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      responses:
        '200':
          description: 成功获取拼单活动详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyDetailResponse'
        '404':
          description: 拼单活动不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /group-buy/activities:
    post:
      tags:
        - 邻里拼单
      summary: 发布拼单活动
      description: 用户发布新的拼单活动
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishGroupBuyRequest'
      responses:
        '201':
          description: 拼单活动发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishGroupBuyResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /group-buy/activities/{activityId}/join:
    post:
      tags:
        - 邻里拼单
      summary: 参与拼单活动
      description: 用户参与指定的拼单活动
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JoinGroupBuyRequest'
      responses:
        '200':
          description: 参与拼单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JoinGroupBuyResponse'
        '400':
          description: 请求参数错误或拼单已满
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

  /group-buy/activities/search:
    post:
      tags:
        - 邻里拼单
      summary: 搜索拼单活动
      description: 根据关键词搜索拼单活动
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupBuySearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /group-buy/activities/{activityId}/leave:
    post:
      tags:
        - 邻里拼单
      summary: 退出拼单活动
      description: 用户退出已参与的拼单活动
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: 退出原因
                  example: "临时有事无法参与"
      responses:
        '200':
          description: 退出拼单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误或无法退出
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BusinessErrorResponse'

  /group-buy/activities/{activityId}/participants:
    get:
      tags:
        - 邻里拼单
      summary: 获取拼单参与者列表
      description: 获取指定拼单活动的参与者列表
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      responses:
        '200':
          description: 成功获取参与者列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyParticipantsResponse'
        '404':
          description: 拼单活动不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /group-buy/categories:
    get:
      tags:
        - 邻里拼单
      summary: 获取拼单分类列表
      description: 获取所有拼单活动分类信息
      responses:
        '200':
          description: 成功获取分类列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyCategoriesResponse'

  /group-buy/my-activities/list:
    post:
      tags:
        - 邻里拼单
      summary: 获取我的拼单活动
      description: 获取当前用户发布的和参与的拼单活动
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MyGroupBuyActivitiesRequest'
      responses:
        '200':
          description: 成功获取我的拼单活动
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MyGroupBuyActivitiesResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"

  # ==================== 用户中心模块 ====================
  /profile/info:
    get:
      tags:
        - 用户中心
      summary: 获取用户信息
      description: 获取当前登录用户的详细信息
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 成功获取用户信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationErrorResponse'
              example:
                success: false
                data: null
                traceId: "trace_123456789"
                err:
                  code: "AUTH_FAILED"
                  msg: "身份验证失败"
                  suggest: "请重新登录"

    put:
      tags:
        - 用户中心
      summary: 更新用户信息
      description: 更新当前登录用户的基本信息
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: 用户信息更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /profile/favorites/list:
    post:
      tags:
        - 用户中心
      summary: 获取收藏列表
      description: 获取用户的收藏列表，支持分页和筛选
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FavoriteListRequest'
      responses:
        '200':
          description: 成功获取收藏列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FavoriteListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /profile/posts/list:
    post:
      tags:
        - 用户中心
      summary: 获取我的发布列表
      description: 获取用户发布的内容列表，支持分页和筛选
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MyPostsListRequest'
      responses:
        '200':
          description: 成功获取发布列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MyPostsListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  # ==================== 消息通知模块 ====================
  /messages/conversations/list:
    post:
      tags:
        - 消息通知
      summary: 获取会话列表
      description: 获取用户的所有会话列表，支持分页和筛选
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationListRequest'
      responses:
        '200':
          description: 成功获取会话列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /messages/conversations/{conversationId}/messages/list:
    post:
      tags:
        - 消息通知
      summary: 获取会话消息列表
      description: 获取指定会话的消息列表，支持分页和消息定位
      security:
        - BearerAuth: []
      parameters:
        - name: conversationId
          in: path
          required: true
          description: 会话唯一标识ID
          schema:
            type: string
            example: "conversation_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageListRequest'
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessagesResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /notifications/list:
    post:
      tags:
        - 消息通知
      summary: 获取通知列表
      description: 获取用户的通知列表，支持分页和多维度筛选
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationListRequest'
      responses:
        '200':
          description: 成功获取通知列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /messages/conversations/{conversationId}/messages:
    post:
      tags:
        - 消息通知
      summary: 发送消息
      description: 向指定会话发送消息
      security:
        - BearerAuth: []
      parameters:
        - name: conversationId
          in: path
          required: true
          description: 会话唯一标识ID
          schema:
            type: string
            example: "conversation_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageRequest'
      responses:
        '201':
          description: 消息发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '404':
          description: 会话不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotFoundErrorResponse'

  /messages/conversations/{conversationId}/read:
    put:
      tags:
        - 消息通知
      summary: 标记消息为已读
      description: 标记指定会话的消息为已读状态
      security:
        - BearerAuth: []
      parameters:
        - name: conversationId
          in: path
          required: true
          description: 会话唯一标识ID
          schema:
            type: string
            example: "conversation_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                messageId:
                  type: string
                  description: 最后已读消息ID
                  example: "message_789012"
      responses:
        '200':
          description: 标记成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 会话不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /notifications/{notificationId}/read:
    put:
      tags:
        - 消息通知
      summary: 标记通知为已读
      description: 标记指定通知为已读状态
      security:
        - BearerAuth: []
      parameters:
        - name: notificationId
          in: path
          required: true
          description: 通知唯一标识ID
          schema:
            type: string
            example: "notification_123456"
      responses:
        '200':
          description: 标记成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 通知不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  # ==================== 公共服务模块 ====================
  /common/upload/images:
    post:
      tags:
        - 公共服务
      summary: 上传图片
      description: 上传图片文件，支持多张图片上传，通用于各个模块
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 图片文件数组，最多9张
                module:
                  type: string
                  description: 所属模块
                  example: "second-hand"
                  enum: [second-hand, housing, parking, market, group-buy, profile]
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: 上传失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /common/location/geocode:
    post:
      tags:
        - 公共服务
      summary: 地理编码
      description: 将地址转换为经纬度坐标
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GeocodeRequest'
      responses:
        '200':
          description: 地理编码成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GeocodeResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /common/location/reverse-geocode:
    post:
      tags:
        - 公共服务
      summary: 逆地理编码
      description: 将经纬度坐标转换为地址信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReverseGeocodeRequest'
      responses:
        '200':
          description: 逆地理编码成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReverseGeocodeResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # ==================== 通用分页请求Schema ====================
    BasePageRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
              description: 页码
              example: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
              description: 每页数量
              example: 20
            filters:
              type: object
              description: 查询过滤条件
              additionalProperties: true
            sorting:
              type: object
              properties:
                field:
                  type: string
                  description: 排序字段
                  example: "createdAt"
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc
                  description: 排序方向
                  example: "desc"

    BasePageResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  description: 总记录数
                  example: 156
                totalPages:
                  type: integer
                  description: 总页数
                  example: 8
                list:
                  type: array
                  items:
                    type: object
                    description: 具体的数据项，由各个模块的Response定义具体类型
                  description: 数据列表

    # ==================== 用户认证相关Schema ====================
    LoginRequest:
      type: object
      required:
        - phone
        - password
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        password:
          type: string
          description: 登录密码
          example: "password123"
          minLength: 6
          maxLength: 20
        rememberMe:
          type: boolean
          description: 是否记住密码
          example: true
          default: false
        deviceInfo:
          $ref: '#/components/schemas/DeviceInfo'

    LoginResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  $ref: '#/components/schemas/UserInfo'
                tokens:
                  $ref: '#/components/schemas/TokenInfo'
                permissions:
                  type: array
                  items:
                    type: string
                  description: 用户权限列表
                  example: ["user:read", "user:write", "community:join"]

    RegisterRequest:
      type: object
      required:
        - phone
        - verificationCode
        - password
        - confirmPassword
        - agreementAccepted
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        verificationCode:
          type: string
          description: 短信验证码
          example: "123456"
          pattern: '^\d{6}$'
        password:
          type: string
          description: 登录密码
          example: "Password123!"
          minLength: 6
          maxLength: 20
        confirmPassword:
          type: string
          description: 确认密码
          example: "Password123!"
          minLength: 6
          maxLength: 20
        nickname:
          type: string
          description: 用户昵称
          example: "张三"
          maxLength: 20
        agreementAccepted:
          type: boolean
          description: 是否同意用户协议
          example: true
        inviteCode:
          type: string
          description: 邀请码（可选）
          example: "INVITE123"
          maxLength: 20
        deviceInfo:
          $ref: '#/components/schemas/DeviceInfo'

    RegisterResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  $ref: '#/components/schemas/UserInfo'
                tokens:
                  $ref: '#/components/schemas/TokenInfo'

    SendSmsRequest:
      type: object
      required:
        - phone
        - type
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        type:
          type: string
          description: 短信类型
          example: "register"
          enum: [register, login, reset_password, change_phone]
        captcha:
          type: object
          properties:
            token:
              type: string
              description: 图形验证码令牌
              example: "captcha_token_123"
            code:
              type: string
              description: 图形验证码
              example: "ABCD"

    SendSmsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                sessionId:
                  type: string
                  description: 会话ID
                  example: "sms_session_123456"
                expiresIn:
                  type: integer
                  description: 验证码有效期（秒）
                  example: 300
                cooldown:
                  type: integer
                  description: 重新发送冷却时间（秒）
                  example: 60
                maskedPhone:
                  type: string
                  description: 脱敏手机号
                  example: "138****8000"

    VerifySmsRequest:
      type: object
      required:
        - phone
        - verificationCode
        - sessionId
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        verificationCode:
          type: string
          description: 短信验证码
          example: "123456"
          pattern: '^\d{6}$'
        sessionId:
          type: string
          description: 短信会话ID
          example: "sms_session_123456"

    VerifySmsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                verified:
                  type: boolean
                  description: 是否验证成功
                  example: true
                verifyToken:
                  type: string
                  description: 验证令牌（用于后续操作）
                  example: "verify_token_789012"
                expiresIn:
                  type: integer
                  description: 验证令牌有效期（秒）
                  example: 1800

    ResetPasswordRequest:
      type: object
      required:
        - phone
        - verificationCode
        - newPassword
        - confirmPassword
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        verificationCode:
          type: string
          description: 短信验证码
          example: "123456"
          pattern: '^\d{6}$'
        newPassword:
          type: string
          description: 新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
        confirmPassword:
          type: string
          description: 确认新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
        sessionId:
          type: string
          description: 短信会话ID
          example: "sms_session_123456"

    ChangePasswordRequest:
      type: object
      required:
        - oldPassword
        - newPassword
        - confirmPassword
      properties:
        oldPassword:
          type: string
          description: 原密码
          example: "OldPassword123!"
          minLength: 6
          maxLength: 20
        newPassword:
          type: string
          description: 新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
        confirmPassword:
          type: string
          description: 确认新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20

    OAuthUrlResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                authUrl:
                  type: string
                  description: 第三方授权URL
                  example: "https://open.weixin.qq.com/connect/oauth2/authorize?appid=xxx&redirect_uri=xxx&response_type=code&scope=snsapi_userinfo&state=xxx"
                state:
                  type: string
                  description: 状态参数
                  example: "state_123456"
                expiresIn:
                  type: integer
                  description: 授权链接有效期（秒）
                  example: 600

    OAuthCallbackRequest:
      type: object
      required:
        - code
      properties:
        code:
          type: string
          description: 授权码
          example: "auth_code_123456"
        state:
          type: string
          description: 状态参数
          example: "state_123456"
        error:
          type: string
          description: 错误信息（授权失败时）
          example: "access_denied"

    RefreshTokenRequest:
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string
          description: 刷新令牌
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    RefreshTokenResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/TokenInfo'

    # ==================== 通用数据模型Schema ====================
    UserInfo:
      type: object
      properties:
        id:
          type: string
          description: 用户ID
          example: "user_123456"
        phone:
          type: string
          description: 手机号码（脱敏）
          example: "138****8000"
        nickname:
          type: string
          description: 用户昵称
          example: "张三"
        avatar:
          type: string
          description: 用户头像
          example: "https://images.example.com/avatar1.jpg"
        gender:
          type: string
          description: 性别
          example: "male"
          enum: [male, female, unknown]
        status:
          type: string
          description: 账户状态
          example: "active"
          enum: [active, inactive, suspended]
        isVerified:
          type: boolean
          description: 是否已实名认证
          example: true
        createTime:
          type: string
          format: date-time
          description: 注册时间
          example: "2024-01-15T10:30:00Z"
        lastLoginTime:
          type: string
          format: date-time
          description: 上次登录时间
          example: "2024-01-15T10:30:00Z"

    TokenInfo:
      type: object
      properties:
        accessToken:
          type: string
          description: 访问令牌
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        refreshToken:
          type: string
          description: 刷新令牌
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        expiresIn:
          type: integer
          description: 访问令牌过期时间（秒）
          example: 7200
        tokenType:
          type: string
          description: 令牌类型
          example: "Bearer"

    DeviceInfo:
      type: object
      properties:
        deviceId:
          type: string
          description: 设备唯一标识
          example: "device_123456"
        deviceType:
          type: string
          description: 设备类型
          example: "mobile"
          enum: [mobile, tablet, desktop]
        platform:
          type: string
          description: 平台信息
          example: "iOS 17.0"
        appVersion:
          type: string
          description: 应用版本
          example: "1.0.0"

    LocationInfo:
      type: object
      properties:
        latitude:
          type: number
          description: 纬度
          example: 30.2741
        longitude:
          type: number
          description: 经度
          example: 120.1551
        address:
          type: string
          description: 详细地址
          example: "杭州市拱墅区春题·杭玥府"
        province:
          type: string
          description: 省份
          example: "浙江省"
        city:
          type: string
          description: 城市
          example: "杭州市"
        district:
          type: string
          description: 区县
          example: "拱墅区"

    # ==================== 社区管理相关Schema ====================
    CommunityListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  description: 搜索关键词
                  example: "春题"
                  maxLength: 100
                location:
                  $ref: '#/components/schemas/LocationInfo'
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 10
                  default: 10
                verified:
                  type: boolean
                  description: 是否只显示已认证小区
                  example: true
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [distance, userCount, name, createdAt]
                  default: distance
                  description: 排序字段
                order:
                  type: string
                  enum: [asc, desc]
                  default: asc
                  description: 排序方向

    CommunityListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 156
                totalPages:
                  type: integer
                  example: 8
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/CommunityItem'

    CommunityItem:
      type: object
      properties:
        id:
          type: string
          description: 小区ID
          example: "community_123456"
        name:
          type: string
          description: 小区名称
          example: "春题·杭玥府"
        address:
          type: string
          description: 小区地址
          example: "杭州市拱墅区"
        location:
          $ref: '#/components/schemas/LocationInfo'
        buildingCount:
          type: integer
          description: 楼栋数量
          example: 15
        householdCount:
          type: integer
          description: 户数
          example: 1200
        userCount:
          type: integer
          description: 注册用户数
          example: 856
        isVerified:
          type: boolean
          description: 是否已认证
          example: true
        distance:
          type: number
          description: 距离（公里）
          example: 1.2

    CommunityDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/CommunityDetail'

    CommunityDetail:
      allOf:
        - $ref: '#/components/schemas/CommunityItem'
        - type: object
          properties:
            description:
              type: string
              description: 小区描述
              example: "高端住宅小区，环境优美，配套齐全"
            facilities:
              type: array
              items:
                type: string
              description: 小区设施
              example: ["游泳池", "健身房", "儿童乐园", "地下停车场"]
            propertyCompany:
              type: string
              description: 物业公司
              example: "绿城物业"
            images:
              type: array
              items:
                type: string
              description: 小区图片
              example: ["https://images.example.com/community1.jpg"]

    JoinCommunityRequest:
      type: object
      required:
        - communityId
        - buildingNumber
        - unitNumber
        - roomNumber
      properties:
        communityId:
          type: string
          description: 小区ID
          example: "community_123456"
        buildingNumber:
          type: string
          description: 楼栋号
          example: "5栋"
        unitNumber:
          type: string
          description: 单元号
          example: "1单元"
        roomNumber:
          type: string
          description: 房间号
          example: "1302"
        ownershipType:
          type: string
          description: 房屋性质
          example: "owner"
          enum: [owner, tenant, family_member]
        verificationImages:
          type: array
          items:
            type: string
          description: 验证图片（房产证、租赁合同等）
          example: ["https://images.example.com/cert1.jpg"]

    JoinCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                applicationId:
                  type: string
                  description: 申请ID
                  example: "app_123456"
                status:
                  type: string
                  description: 申请状态
                  example: "pending"
                  enum: [pending, approved, rejected]
                estimatedReviewTime:
                  type: string
                  description: 预计审核时间
                  example: "1-3个工作日"

    BindCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                communityId:
                  type: string
                  description: 绑定的小区ID
                  example: "community_123456"
                communityName:
                  type: string
                  description: 绑定的小区名称
                  example: "春题·杭玥府"
                bindTime:
                  type: string
                  format: date-time
                  description: 绑定时间
                  example: "2024-01-15T10:30:00Z"

    RegisterCommunityRequest:
      type: object
      required:
        - communityName
        - province
        - city
        - district
        - address
        - applicantName
        - applicantPhone
        - applicantIdentity
      properties:
        communityName:
          type: string
          description: 小区名称
          example: "阳光花园"
          maxLength: 100
        province:
          type: string
          description: 省份
          example: "浙江省"
        city:
          type: string
          description: 城市
          example: "杭州市"
        district:
          type: string
          description: 区域
          example: "拱墅区"
        address:
          type: string
          description: 详细地址
          example: "拱墅区湖州街200号"
          maxLength: 200
        applicantName:
          type: string
          description: 申请人姓名
          example: "张三"
          maxLength: 50
        applicantPhone:
          type: string
          description: 申请人联系电话
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        applicantIdentity:
          type: string
          description: 申请人身份
          example: "业主"
          enum: ["业主", "物业", "租户"]
        buildingInfo:
          type: object
          properties:
            building:
              type: string
              description: 楼栋信息
              example: "3栋2单元"
            roomNumber:
              type: string
              description: 房间号
              example: "1201"
        additionalNotes:
          type: string
          description: 补充说明
          example: "小区环境优美，管理规范"
          maxLength: 500
        documents:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 证明材料图片URL
                example: "https://images.example.com/document1.jpg"
              type:
                type: string
                enum: [property_certificate, property_management_proof, residence_proof, other]
                description: 证明材料类型
                example: "property_certificate"
              description:
                type: string
                description: 材料描述
                example: "房产证"
          description: 证明材料列表

    RegisterCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                applicationId:
                  type: string
                  description: 申请ID
                  example: "application_789012"
                status:
                  type: string
                  description: 申请状态
                  example: "pending_review"
                  enum: ["pending_review", "approved", "rejected", "under_review"]
                submitTime:
                  type: string
                  format: date-time
                  description: 提交时间
                  example: "2024-01-15T10:30:00Z"
                estimatedReviewTime:
                  type: string
                  description: 预计审核时间
                  example: "3-5个工作日"

    SwitchCommunityResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                previousCommunity:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 之前的小区ID
                      example: "community_123456"
                    name:
                      type: string
                      description: 之前的小区名称
                      example: "春题·杭玥府"
                currentCommunity:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 当前的小区ID
                      example: "community_789012"
                    name:
                      type: string
                      description: 当前的小区名称
                      example: "阳光花园"
                switchTime:
                  type: string
                  format: date-time
                  description: 切换时间
                  example: "2024-01-15T10:30:00Z"

    # ==================== 二手闲置相关Schema ====================
    SecondHandListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  description: 搜索关键词
                  example: "iPhone"
                  maxLength: 100
                category:
                  type: string
                  description: 商品分类
                  example: "手机数码"
                condition:
                  type: string
                  description: 成色筛选
                  example: "95新"
                  enum: ["全新", "99新", "95新", "9成新", "8成新", "7成新", "其他"]
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                      description: 最低价格
                      example: 1000
                    max:
                      type: number
                      description: 最高价格
                      example: 8000
                location:
                  $ref: '#/components/schemas/LocationInfo'
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 5
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, price, viewCount, distance]
                  default: createdAt
                  description: 排序字段
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc
                  description: 排序方向

    SecondHandListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 156
                totalPages:
                  type: integer
                  example: 8
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/SecondHandItem'

    SecondHandItem:
      type: object
      properties:
        id:
          type: string
          description: 商品ID
          example: "product_123456"
        title:
          type: string
          description: 商品标题
          example: "iPhone 14 Pro Max 256GB 深空黑"
        price:
          type: number
          description: 价格
          example: 7999
        originalPrice:
          type: number
          description: 原价
          example: 9999
        condition:
          type: string
          description: 成色
          example: "95新"
        category:
          type: string
          description: 商品分类
          example: "手机数码"
        images:
          type: array
          items:
            type: string
          description: 商品图片
          example: ["https://images.example.com/phone1.jpg"]
        seller:
          type: object
          properties:
            id:
              type: string
              example: "user_789"
            nickname:
              type: string
              example: "张三"
            avatar:
              type: string
              example: "https://images.example.com/avatar1.jpg"
            location:
              type: string
              example: "5栋1单元"
        publishTime:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        viewCount:
          type: integer
          description: 浏览次数
          example: 156
        isFavorited:
          type: boolean
          description: 是否已收藏
          example: false
        distance:
          type: number
          description: 距离（公里）
          example: 0.5

    SecondHandDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/SecondHandDetail'

    SecondHandDetail:
      allOf:
        - $ref: '#/components/schemas/SecondHandItem'
        - type: object
          properties:
            description:
              type: string
              description: 商品详细描述
              example: "个人自用，9.5成新，无磕碰，功能正常，配件齐全"
            specifications:
              type: object
              description: 商品规格参数
              example:
                brand: "Apple"
                model: "iPhone 14 Pro Max"
                storage: "256GB"
                color: "深空黑"
            purchaseInfo:
              type: object
              properties:
                purchaseDate:
                  type: string
                  format: date
                  description: 购买日期
                  example: "2023-10-15"
                purchasePrice:
                  type: number
                  description: 购买价格
                  example: 9999
                warranty:
                  type: string
                  description: 保修情况
                  example: "还有8个月保修"
            tradePreferences:
              type: object
              properties:
                tradeType:
                  type: string
                  description: 交易方式
                  example: "both"
                  enum: [face_to_face, express, both]
                negotiable:
                  type: boolean
                  description: 是否可议价
                  example: true
                acceptExchange:
                  type: boolean
                  description: 是否接受置换
                  example: false

    PublishSecondHandRequest:
      type: object
      required:
        - title
        - price
        - condition
        - category
        - description
        - images
      properties:
        title:
          type: string
          description: 商品标题
          example: "iPhone 14 Pro Max 256GB 深空黑"
          maxLength: 100
        price:
          type: number
          description: 价格
          example: 7999
          minimum: 0.01
        originalPrice:
          type: number
          description: 原价
          example: 9999
        condition:
          type: string
          description: 成色
          example: "95新"
          enum: ["全新", "99新", "95新", "9成新", "8成新", "7成新", "其他"]
        category:
          type: string
          description: 商品分类
          example: "手机数码"
        description:
          type: string
          description: 商品详细描述
          example: "个人自用，9.5成新，无磕碰，功能正常，配件齐全"
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 商品图片URL列表
          example: ["https://images.example.com/phone1.jpg"]
          minItems: 1
          maxItems: 9
        specifications:
          type: object
          description: 商品规格参数
          additionalProperties: true
        purchaseInfo:
          type: object
          properties:
            purchaseDate:
              type: string
              format: date
              description: 购买日期
              example: "2023-10-15"
            purchasePrice:
              type: number
              description: 购买价格
              example: 9999
            warranty:
              type: string
              description: 保修情况
              example: "还有8个月保修"
        tradePreferences:
          type: object
          properties:
            tradeType:
              type: string
              description: 交易方式
              example: "both"
              enum: [face_to_face, express, both]
            negotiable:
              type: boolean
              description: 是否可议价
              example: true
            acceptExchange:
              type: boolean
              description: 是否接受置换
              example: false

    PublishSecondHandResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                productId:
                  type: string
                  description: 商品ID
                  example: "product_789012"
                status:
                  type: string
                  description: 商品状态
                  example: "active"
              enum: [active, pending_review, rejected]
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"

    # ==================== 公共服务相关Schema ====================
    UploadResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                images:
                  type: array
                  items:
                    type: object
                    properties:
                      url:
                        type: string
                        description: 图片URL
                        example: "https://images.example.com/upload1.jpg"
                      filename:
                        type: string
                        description: 文件名
                        example: "upload1.jpg"
                      size:
                        type: integer
                        description: 文件大小（字节）
                        example: 1024000
                      width:
                        type: integer
                        description: 图片宽度
                        example: 1920
                      height:
                        type: integer
                        description: 图片高度
                        example: 1080

    GeocodeRequest:
      type: object
      required:
        - address
      properties:
        address:
          type: string
          description: 地址信息
          example: "杭州市拱墅区春题·杭玥府"
          maxLength: 200

    GeocodeResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/LocationInfo'

    ReverseGeocodeRequest:
      type: object
      required:
        - latitude
        - longitude
      properties:
        latitude:
          type: number
          description: 纬度
          example: 30.2741
        longitude:
          type: number
          description: 经度
          example: 120.1551

    ReverseGeocodeResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/LocationInfo'

    # ==================== 通用响应Schema ====================
    BaseSuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
          description: 请求是否成功
        data:
          description: 响应数据
        traceId:
          type: string
          description: 请求追踪ID
          example: "trace_123456789"
      required:
        - success
        - traceId

    BaseErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
          description: 请求是否成功
        data:
          nullable: true
          example: null
          description: 错误时数据为null
        traceId:
          type: string
          description: 请求追踪ID
          example: "trace_123456789"
        err:
          type: object
          properties:
            code:
              type: string
              description: 错误代码
              example: "INVALID_PARAMS"
            msg:
              type: string
              description: 错误消息
              example: "执行发生错误"
            suggest:
              type: string
              nullable: true
              description: 错误建议
              example: null
          required:
            - code
            - msg
      required:
        - success
        - data
        - traceId
        - err

    # 兼容旧版本的Response（逐步废弃）
    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "title字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"
        traceId:
          type: string
          description: 请求追踪ID
          example: "trace_123456789"

    # ==================== 具体错误响应示例 ====================
    ValidationErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseErrorResponse'
        - type: object
          properties:
            err:
              type: object
              properties:
                code:
                  type: string
                  example: "VALIDATION_ERROR"
                msg:
                  type: string
                  example: "请求参数验证失败"
                suggest:
                  type: string
                  example: "请检查必填字段是否完整"

    AuthenticationErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseErrorResponse'
        - type: object
          properties:
            err:
              type: object
              properties:
                code:
                  type: string
                  example: "AUTH_FAILED"
                msg:
                  type: string
                  example: "身份验证失败"
                suggest:
                  type: string
                  example: "请重新登录"

    NotFoundErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseErrorResponse'
        - type: object
          properties:
            err:
              type: object
              properties:
                code:
                  type: string
                  example: "RESOURCE_NOT_FOUND"
                msg:
                  type: string
                  example: "请求的资源不存在"
                suggest:
                  type: string
                  example: "请检查资源ID是否正确"

    BusinessErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseErrorResponse'
        - type: object
          properties:
            err:
              type: object
              properties:
                code:
                  type: string
                  example: "BUSINESS_ERROR"
                msg:
                  type: string
                  example: "执行发生错误"
                suggest:
                  type: string
                  nullable: true
                  example: null

    # ==================== 二手闲置相关Schema ====================
    UpdateSecondHandRequest:
      type: object
      properties:
        title:
          type: string
          description: 商品标题
          example: "iPhone 14 Pro Max 256GB 深空黑"
          maxLength: 100
        price:
          type: number
          description: 价格
          example: 7999
          minimum: 0.01
        originalPrice:
          type: number
          description: 原价
          example: 9999
        condition:
          type: string
          description: 成色
          example: "95新"
          enum: ["全新", "99新", "95新", "9成新", "8成新", "7成新", "其他"]
        description:
          type: string
          description: 商品详细描述
          example: "个人自用，9.5成新，无磕碰，功能正常，配件齐全"
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 商品图片URL列表
          example: ["https://images.example.com/phone1.jpg"]
          maxItems: 9
        status:
          type: string
          description: 商品状态
          example: "active"
          enum: [active, sold, inactive]

    SecondHandSearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  description: 搜索关键词
                  example: "iPhone"
                  maxLength: 100
                category:
                  type: string
                  description: 商品分类
                  example: "手机数码"
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                      example: 1000
                    max:
                      type: number
                      example: 8000
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [relevance, createdAt, price, viewCount]
                  default: relevance
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    SecondHandCategoriesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                categories:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 分类ID
                        example: "category_electronics"
                      name:
                        type: string
                        description: 分类名称
                        example: "手机数码"
                      icon:
                        type: string
                        description: 分类图标
                        example: "fas fa-mobile-alt"
                      productCount:
                        type: integer
                        description: 该分类下的商品数量
                        example: 156

    # ==================== 房屋租赁相关Schema ====================
    UpdateHousingRequest:
      type: object
      properties:
        title:
          type: string
          description: 房源标题
          maxLength: 100
        price:
          type: number
          description: 月租金
          minimum: 0.01
        roomType:
          type: string
          description: 户型
          example: "2室1厅1卫"
        area:
          type: number
          description: 面积（平方米）
          example: 85.5
        description:
          type: string
          description: 房源描述
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 房源图片URL列表
          maxItems: 20
        facilities:
          type: array
          items:
            type: string
          description: 房源设施
          example: ["空调", "洗衣机", "冰箱", "WiFi"]
        status:
          type: string
          description: 房源状态
          enum: [available, rented, maintenance]

    HousingSearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                    max:
                      type: number
                roomType:
                  type: string
                areaRange:
                  type: object
                  properties:
                    min:
                      type: number
                    max:
                      type: number
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [relevance, createdAt, price, area]
                  default: relevance
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    # ==================== 停车位相关Schema ====================
    ParkingListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                parkingType:
                  type: string
                  enum: [indoor, outdoor, underground]
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                      minimum: 0
                    max:
                      type: number
                      minimum: 0
                location:
                  $ref: '#/components/schemas/LocationInfo'
                rentType:
                  type: string
                  enum: [hourly, daily, monthly, sale]
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, price, distance]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    ParkingListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 45
                totalPages:
                  type: integer
                  example: 3
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/ParkingItem'

    ParkingItem:
      type: object
      properties:
        id:
          type: string
          description: 停车位ID
          example: "space_123456"
        title:
          type: string
          description: 停车位标题
          example: "地下车库固定车位出租"
        price:
          type: number
          description: 月租金或售价
          example: 300
        parkingType:
          type: string
          description: 停车位类型
          enum: [indoor, outdoor, underground]
          example: "underground"
        location:
          $ref: '#/components/schemas/LocationInfo'
        owner:
          type: object
          properties:
            id:
              type: string
              example: "user_789"
            nickname:
              type: string
              example: "车位主"
            avatar:
              type: string
              example: "https://images.example.com/avatar1.jpg"
        images:
          type: array
          items:
            type: string
          description: 停车位图片
          example: ["https://images.example.com/parking1.jpg"]
        publishTime:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        status:
          type: string
          enum: [available, rented, sold, maintenance]
          example: "available"

    ParkingDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ParkingDetail'

    ParkingDetail:
      allOf:
        - $ref: '#/components/schemas/ParkingItem'
        - type: object
          properties:
            description:
              type: string
              description: 停车位详细描述
              example: "地下一层，位置好找，24小时监控"
            availableTime:
              type: object
              properties:
                startTime:
                  type: string
                  description: 可用开始时间
                  example: "08:00"
                endTime:
                  type: string
                  description: 可用结束时间
                  example: "18:00"
            rentType:
              type: string
              description: 租赁类型
              enum: [hourly, daily, monthly, sale]
              example: "monthly"
            contactInfo:
              type: object
              properties:
                phone:
                  type: string
                  description: 联系电话
                  example: "138****5678"
                wechat:
                  type: string
                  description: 微信号
                  example: "parking_owner"

    PublishParkingRequest:
      type: object
      required:
        - title
        - price
        - parkingType
        - location
      properties:
        title:
          type: string
          description: 停车位标题
          example: "地下车库固定车位出租"
          maxLength: 100
        price:
          type: number
          description: 月租金或售价
          example: 300
          minimum: 0.01
        parkingType:
          type: string
          description: 停车位类型
          example: "underground"
          enum: [indoor, outdoor, underground]
        description:
          type: string
          description: 停车位描述
          example: "地下一层，位置好找，24小时监控"
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 停车位图片URL列表
          example: ["https://images.example.com/parking1.jpg"]
          maxItems: 9
        location:
          $ref: '#/components/schemas/LocationInfo'
        availableTime:
          type: object
          properties:
            startTime:
              type: string
              description: 可用开始时间
              example: "08:00"
            endTime:
              type: string
              description: 可用结束时间
              example: "18:00"
        rentType:
          type: string
          description: 租赁类型
          example: "monthly"
          enum: [hourly, daily, monthly, sale]

    PublishParkingResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                spaceId:
                  type: string
                  description: 停车位ID
                  example: "space_789012"
                status:
                  type: string
                  description: 停车位状态
                  example: "active"
                publishTime:
                  type: string
                  format: date-time
                  description: 发布时间
                  example: "2024-01-15T10:30:00Z"

    UpdateParkingRequest:
      type: object
      properties:
        title:
          type: string
          description: 停车位标题
          maxLength: 100
        price:
          type: number
          description: 月租金或售价
          minimum: 0.01
        description:
          type: string
          description: 停车位描述
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 停车位图片URL列表
          maxItems: 9
        availableTime:
          type: object
          properties:
            startTime:
              type: string
              description: 可用开始时间
            endTime:
              type: string
              description: 可用结束时间
        status:
          type: string
          description: 停车位状态
          enum: [available, rented, sold, maintenance]

    # ==================== 消息通知相关Schema ====================
    SendMessageRequest:
      type: object
      required:
        - type
        - content
      properties:
        type:
          type: string
          description: 消息类型
          example: "text"
          enum: [text, image, voice, video, file]
        content:
          type: string
          description: 消息内容
          example: "你好，请问这个商品还在吗？"
          maxLength: 1000
        attachments:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                description: 附件类型
                example: "image"
                enum: [image, voice, video, file]
              url:
                type: string
                description: 附件URL
                example: "https://images.example.com/attachment1.jpg"
              size:
                type: integer
                description: 附件大小（字节）
                example: 1024000
              duration:
                type: integer
                description: 音视频时长（秒）
                example: 30
          description: 消息附件
        replyTo:
          type: string
          description: 回复的消息ID
          example: "message_456789"

    SendMessageResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                messageId:
                  type: string
                  description: 消息ID
                  example: "message_789012"
                sendTime:
                  type: string
                  format: date-time
                  description: 发送时间
                  example: "2024-01-15T14:30:00Z"
                status:
                  type: string
                  description: 消息状态
                  example: "sent"
                  enum: [sending, sent, delivered, read, failed]

    # ==================== 邻里集市相关Schema ====================
    PublishMarketProductRequest:
      type: object
      required:
        - title
        - price
        - category
        - description
        - images
      properties:
        title:
          type: string
          description: 农产品标题
          example: "新鲜有机西红柿"
          maxLength: 100
        price:
          type: number
          description: 价格（元/斤）
          example: 8.5
          minimum: 0.01
        category:
          type: string
          description: 农产品分类
          example: "蔬菜类"
        description:
          type: string
          description: 农产品详细描述
          example: "自家种植的有机西红柿，无农药，口感佳"
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 农产品图片URL列表
          example: ["https://images.example.com/tomato1.jpg"]
          minItems: 1
          maxItems: 9
        unit:
          type: string
          description: 计量单位
          example: "斤"
        stock:
          type: number
          description: 库存数量
          example: 100
        location:
          $ref: '#/components/schemas/LocationInfo'
        harvestDate:
          type: string
          format: date
          description: 采摘日期
          example: "2024-01-15"

    PublishMarketProductResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                productId:
                  type: string
                  description: 农产品ID
                  example: "market_product_789012"
                status:
                  type: string
                  description: 农产品状态
                  example: "active"
                publishTime:
                  type: string
                  format: date-time
                  description: 发布时间
                  example: "2024-01-15T10:30:00Z"

    MarketProductSearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                category:
                  type: string
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                    max:
                      type: number
                freshness:
                  type: string
                  enum: [today, within_3_days, within_week]
                location:
                  $ref: '#/components/schemas/LocationInfo'
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [relevance, createdAt, price, freshness]
                  default: relevance
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    MarketCategoriesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                categories:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 分类ID
                        example: "category_vegetables"
                      name:
                        type: string
                        description: 分类名称
                        example: "蔬菜类"
                      icon:
                        type: string
                        description: 分类图标
                        example: "fas fa-carrot"
                      productCount:
                        type: integer
                        description: 该分类下的农产品数量
                        example: 89

    FarmerListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                location:
                  $ref: '#/components/schemas/LocationInfo'
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 10
                verified:
                  type: boolean
                  description: 是否只显示认证农户
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [distance, rating, productCount]
                  default: distance
                order:
                  type: string
                  enum: [asc, desc]
                  default: asc

    FarmerListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 45
                totalPages:
                  type: integer
                  example: 3
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/FarmerItem'

    FarmerItem:
      type: object
      properties:
        id:
          type: string
          description: 农户ID
          example: "farmer_123456"
        name:
          type: string
          description: 农户姓名
          example: "张大叔"
        avatar:
          type: string
          description: 农户头像
          example: "https://images.example.com/farmer1.jpg"
        farmName:
          type: string
          description: 农场名称
          example: "绿色家园农场"
        location:
          $ref: '#/components/schemas/LocationInfo'
        rating:
          type: number
          description: 评分
          example: 4.8
        productCount:
          type: integer
          description: 在售农产品数量
          example: 12
        verified:
          type: boolean
          description: 是否认证农户
          example: true

    FarmerDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/FarmerDetail'

    FarmerDetail:
      allOf:
        - $ref: '#/components/schemas/FarmerItem'
        - type: object
          properties:
            description:
              type: string
              description: 农户介绍
              example: "专业种植有机蔬菜20年，绿色无污染"
            farmImages:
              type: array
              items:
                type: string
              description: 农场图片
              example: ["https://images.example.com/farm1.jpg"]
            certifications:
              type: array
              items:
                type: string
              description: 认证信息
              example: ["有机认证", "绿色食品认证"]
            contactInfo:
              type: object
              properties:
                phone:
                  type: string
                  description: 联系电话
                  example: "138****5678"
                wechat:
                  type: string
                  description: 微信号
                  example: "farmer_zhang"

    # ==================== 邻里拼单相关Schema ====================
    GroupBuySearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                category:
                  type: string
                status:
                  type: string
                  enum: [active, completed, expired, cancelled]
                location:
                  $ref: '#/components/schemas/LocationInfo'
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [relevance, createdAt, deadline, participants]
                  default: relevance
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    GroupBuyParticipantsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                participants:
                  type: array
                  items:
                    type: object
                    properties:
                      userId:
                        type: string
                        description: 用户ID
                        example: "user_123456"
                      nickname:
                        type: string
                        description: 用户昵称
                        example: "小明"
                      avatar:
                        type: string
                        description: 用户头像
                        example: "https://images.example.com/avatar1.jpg"
                      joinTime:
                        type: string
                        format: date-time
                        description: 参与时间
                        example: "2024-01-15T10:30:00Z"
                      quantity:
                        type: integer
                        description: 参与数量
                        example: 2

    GroupBuyCategoriesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                categories:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 分类ID
                        example: "category_food"
                      name:
                        type: string
                        description: 分类名称
                        example: "食品饮料"
                      icon:
                        type: string
                        description: 分类图标
                        example: "fas fa-utensils"
                      activityCount:
                        type: integer
                        description: 该分类下的拼单活动数量
                        example: 23

    MyGroupBuyActivitiesRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                type:
                  type: string
                  enum: [created, joined]
                  description: 活动类型：created-我发起的，joined-我参与的
                status:
                  type: string
                  enum: [active, completed, expired, cancelled]
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, deadline]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    MyGroupBuyActivitiesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 15
                totalPages:
                  type: integer
                  example: 2
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/GroupBuyItem'

    GroupBuyItem:
      type: object
      properties:
        id:
          type: string
          description: 拼单活动ID
          example: "activity_123456"
        title:
          type: string
          description: 拼单活动标题
          example: "团购优质苹果，10斤装"
        category:
          type: string
          description: 拼单分类
          example: "水果生鲜"
        targetPrice:
          type: number
          description: 目标价格
          example: 50.0
        currentPrice:
          type: number
          description: 当前价格
          example: 65.0
        minParticipants:
          type: integer
          description: 最少参与人数
          example: 10
        maxParticipants:
          type: integer
          description: 最多参与人数
          example: 50
        currentParticipants:
          type: integer
          description: 当前参与人数
          example: 25
        deadline:
          type: string
          format: date-time
          description: 拼单截止时间
          example: "2024-01-20T18:00:00Z"
        status:
          type: string
          description: 拼单状态
          enum: [active, completed, expired, cancelled]
          example: "active"
        organizer:
          type: object
          properties:
            id:
              type: string
              example: "user_789"
            nickname:
              type: string
              example: "团长小李"
            avatar:
              type: string
              example: "https://images.example.com/avatar1.jpg"
        location:
          $ref: '#/components/schemas/LocationInfo'
        images:
          type: array
          items:
            type: string
          description: 拼单商品图片
          example: ["https://images.example.com/apple1.jpg"]
        createTime:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-15T10:30:00Z"
        progress:
          type: number
          description: 拼单进度百分比
          example: 50.0

    # ==================== 订单相关Schema ====================
    MarketOrderListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                status:
                  type: string
                  enum: [pending, confirmed, shipped, delivered, cancelled]
                dateRange:
                  type: object
                  properties:
                    startDate:
                      type: string
                      format: date
                    endDate:
                      type: string
                      format: date
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, totalAmount]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    MarketOrderListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 25
                totalPages:
                  type: integer
                  example: 3
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/MarketOrderItem'

    MarketOrderItem:
      type: object
      properties:
        orderId:
          type: string
          description: 订单ID
          example: "order_123456"
        productInfo:
          type: object
          properties:
            productId:
              type: string
              example: "product_789"
            title:
              type: string
              example: "新鲜有机西红柿"
            image:
              type: string
              example: "https://images.example.com/tomato1.jpg"
            price:
              type: number
              example: 8.5
            quantity:
              type: number
              example: 5
        totalAmount:
          type: number
          description: 订单总金额
          example: 42.5
        status:
          type: string
          description: 订单状态
          enum: [pending, confirmed, shipped, delivered, cancelled]
          example: "confirmed"
        createTime:
          type: string
          format: date-time
          description: 下单时间
          example: "2024-01-15T10:30:00Z"

    MarketOrderDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/MarketOrderDetail'

    MarketOrderDetail:
      allOf:
        - $ref: '#/components/schemas/MarketOrderItem'
        - type: object
          properties:
            farmerInfo:
              type: object
              properties:
                farmerId:
                  type: string
                  example: "farmer_456"
                name:
                  type: string
                  example: "张大叔"
                farmName:
                  type: string
                  example: "绿色家园农场"
                phone:
                  type: string
                  example: "138****5678"
            deliveryInfo:
              type: object
              properties:
                address:
                  type: string
                  description: 收货地址
                  example: "北京市朝阳区xxx小区"
                contactName:
                  type: string
                  description: 收货人姓名
                  example: "李先生"
                contactPhone:
                  type: string
                  description: 收货人电话
                  example: "139****1234"
                deliveryTime:
                  type: string
                  format: date-time
                  description: 预计送达时间
                  example: "2024-01-16T14:00:00Z"
            paymentInfo:
              type: object
              properties:
                paymentMethod:
                  type: string
                  description: 支付方式
                  example: "微信支付"
                paymentTime:
                  type: string
                  format: date-time
                  description: 支付时间
                  example: "2024-01-15T10:35:00Z"

    # ==================== 占位符Schema（其他模块） ====================
    # 以下Schema为其他模块的占位符，实际使用时需要根据具体业务需求完善
    HousingListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                    max:
                      type: number
                roomType:
                  type: string
                location:
                  $ref: '#/components/schemas/LocationInfo'
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, price, area]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    HousingListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 156
                totalPages:
                  type: integer
                  example: 8
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/HousingItem'

    HousingItem:
      type: object
      properties:
        id:
          type: string
          description: 房源ID
          example: "property_123456"
        title:
          type: string
          description: 房源标题
          example: "阳光花园 2室1厅精装修"
        price:
          type: number
          description: 月租金
          example: 3200
        roomType:
          type: string
          description: 户型
          example: "2室1厅1卫"
        area:
          type: number
          description: 面积（平方米）
          example: 85.5
        images:
          type: array
          items:
            type: string
          description: 房源图片
          example: ["https://images.example.com/house1.jpg"]
        landlord:
          type: object
          properties:
            id:
              type: string
              example: "user_789"
            nickname:
              type: string
              example: "李房东"
            avatar:
              type: string
              example: "https://images.example.com/avatar1.jpg"
        location:
          $ref: '#/components/schemas/LocationInfo'
        publishTime:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        status:
          type: string
          enum: [available, rented, maintenance]
          example: "available"

    HousingDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/HousingDetail'

    HousingDetail:
      allOf:
        - $ref: '#/components/schemas/HousingItem'
        - type: object
          properties:
            description:
              type: string
              description: 房源详细描述
              example: "精装修两室一厅，家具家电齐全，拎包入住"
            facilities:
              type: array
              items:
                type: string
              description: 房源设施
              example: ["空调", "洗衣机", "冰箱", "WiFi", "电视"]
            rentRequirements:
              type: object
              properties:
                deposit:
                  type: number
                  description: 押金
                  example: 3200
                minRentPeriod:
                  type: integer
                  description: 最短租期（月）
                  example: 6
                petAllowed:
                  type: boolean
                  description: 是否允许养宠物
                  example: false

    PublishHousingRequest:
      type: object
      required:
        - title
        - price
        - roomType
        - area
        - description
        - images
      properties:
        title:
          type: string
          description: 房源标题
          example: "阳光花园 2室1厅精装修"
          maxLength: 100
        price:
          type: number
          description: 月租金
          example: 3200
          minimum: 0.01
        roomType:
          type: string
          description: 户型
          example: "2室1厅1卫"
        area:
          type: number
          description: 面积（平方米）
          example: 85.5
          minimum: 1
        description:
          type: string
          description: 房源详细描述
          example: "精装修两室一厅，家具家电齐全，拎包入住"
          maxLength: 1000
        images:
          type: array
          items:
            type: string
          description: 房源图片URL列表
          example: ["https://images.example.com/house1.jpg"]
          minItems: 1
          maxItems: 20
        facilities:
          type: array
          items:
            type: string
          description: 房源设施
          example: ["空调", "洗衣机", "冰箱", "WiFi"]
        location:
          $ref: '#/components/schemas/LocationInfo'
        rentRequirements:
          type: object
          properties:
            deposit:
              type: number
              description: 押金
              example: 3200
            minRentPeriod:
              type: integer
              description: 最短租期（月）
              example: 6
            petAllowed:
              type: boolean
              description: 是否允许养宠物
              example: false

    PublishHousingResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                propertyId:
                  type: string
                  description: 房源ID
                  example: "property_789012"
                status:
                  type: string
                  description: 房源状态
                  example: "active"
                publishTime:
                  type: string
                  format: date-time
                  description: 发布时间
                  example: "2024-01-15T10:30:00Z"



    MarketProductListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                category:
                  type: string
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                    max:
                      type: number
                freshness:
                  type: string
                  enum: [today, within_3_days, within_week]
                location:
                  $ref: '#/components/schemas/LocationInfo'
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, price, distance, freshness]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    MarketProductListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 89
                totalPages:
                  type: integer
                  example: 5
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/MarketProductItem'

    MarketProductItem:
      type: object
      properties:
        id:
          type: string
          description: 农产品ID
          example: "market_product_123456"
        title:
          type: string
          description: 农产品标题
          example: "新鲜有机西红柿"
        price:
          type: number
          description: 价格（元/斤）
          example: 8.5
        category:
          type: string
          description: 农产品分类
          example: "蔬菜类"
        unit:
          type: string
          description: 计量单位
          example: "斤"
        stock:
          type: number
          description: 库存数量
          example: 100
        farmer:
          type: object
          properties:
            id:
              type: string
              example: "farmer_789"
            name:
              type: string
              example: "张大叔"
            farmName:
              type: string
              example: "绿色家园农场"
            avatar:
              type: string
              example: "https://images.example.com/farmer1.jpg"
        location:
          $ref: '#/components/schemas/LocationInfo'
        images:
          type: array
          items:
            type: string
          description: 农产品图片
          example: ["https://images.example.com/tomato1.jpg"]
        harvestDate:
          type: string
          format: date
          description: 采摘日期
          example: "2024-01-15"
        publishTime:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        status:
          type: string
          enum: [available, sold_out, offline]
          example: "available"

    MarketProductDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/MarketProductDetail'

    MarketProductDetail:
      allOf:
        - $ref: '#/components/schemas/MarketProductItem'
        - type: object
          properties:
            description:
              type: string
              description: 农产品详细描述
              example: "自家种植的有机西红柿，无农药，口感佳"
            certifications:
              type: array
              items:
                type: string
              description: 认证信息
              example: ["有机认证", "绿色食品认证"]
            nutritionInfo:
              type: object
              properties:
                calories:
                  type: number
                  description: 热量（每100g）
                  example: 18
                vitamin_c:
                  type: number
                  description: 维生素C含量（mg/100g）
                  example: 25

    CreateMarketOrderRequest:
      type: object
      required:
        - productId
        - quantity
        - deliveryAddress
      properties:
        productId:
          type: string
          description: 农产品ID
          example: "market_product_123456"
        quantity:
          type: number
          description: 购买数量
          example: 5
          minimum: 0.1
        deliveryAddress:
          type: object
          properties:
            address:
              type: string
              description: 收货地址
              example: "北京市朝阳区xxx小区"
            contactName:
              type: string
              description: 收货人姓名
              example: "李先生"
            contactPhone:
              type: string
              description: 收货人电话
              example: "139****1234"
        remark:
          type: string
          description: 订单备注
          example: "请在下午送达"
          maxLength: 200

    CreateMarketOrderResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                orderId:
                  type: string
                  description: 订单ID
                  example: "order_789012"
                totalAmount:
                  type: number
                  description: 订单总金额
                  example: 42.5
                status:
                  type: string
                  description: 订单状态
                  example: "pending"
                createTime:
                  type: string
                  format: date-time
                  description: 创建时间
                  example: "2024-01-15T10:30:00Z"

    GroupBuyListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                category:
                  type: string
                status:
                  type: string
                  enum: [active, completed, expired, cancelled]
                location:
                  $ref: '#/components/schemas/LocationInfo'
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, deadline, participants]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    GroupBuyListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 23
                totalPages:
                  type: integer
                  example: 2
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/GroupBuyItem'

    GroupBuyDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/GroupBuyDetail'

    GroupBuyDetail:
      allOf:
        - $ref: '#/components/schemas/GroupBuyItem'
        - type: object
          properties:
            description:
              type: string
              description: 拼单活动详细描述
              example: "团购优质苹果，产地直供，新鲜美味"
            rules:
              type: object
              properties:
                deliveryMethod:
                  type: string
                  description: 配送方式
                  example: "自提"
                  enum: [delivery, pickup, both]
                paymentMethod:
                  type: string
                  description: 支付方式
                  example: "微信支付"
                  enum: [wechat, alipay, cash]
                refundPolicy:
                  type: string
                  description: 退款政策
                  example: "拼单失败全额退款"
            participants:
              type: array
              items:
                type: object
                properties:
                  userId:
                    type: string
                    example: "user_123"
                  nickname:
                    type: string
                    example: "小明"
                  avatar:
                    type: string
                    example: "https://images.example.com/avatar1.jpg"
                  joinTime:
                    type: string
                    format: date-time
                    example: "2024-01-15T10:30:00Z"
                  quantity:
                    type: integer
                    example: 2

    PublishGroupBuyRequest:
      type: object
      required:
        - title
        - category
        - targetPrice
        - currentPrice
        - minParticipants
        - maxParticipants
        - deadline
        - images
      properties:
        title:
          type: string
          description: 拼单活动标题
          example: "团购优质苹果，10斤装"
          maxLength: 100
        category:
          type: string
          description: 拼单分类
          example: "水果生鲜"
        description:
          type: string
          description: 拼单活动描述
          example: "团购优质苹果，产地直供，新鲜美味"
          maxLength: 1000
        targetPrice:
          type: number
          description: 目标价格
          example: 50.0
          minimum: 0.01
        currentPrice:
          type: number
          description: 当前价格
          example: 65.0
          minimum: 0.01
        minParticipants:
          type: integer
          description: 最少参与人数
          example: 10
          minimum: 2
        maxParticipants:
          type: integer
          description: 最多参与人数
          example: 50
          minimum: 2
        deadline:
          type: string
          format: date-time
          description: 拼单截止时间
          example: "2024-01-20T18:00:00Z"
        images:
          type: array
          items:
            type: string
          description: 拼单商品图片URL列表
          example: ["https://images.example.com/apple1.jpg"]
          minItems: 1
          maxItems: 9
        location:
          $ref: '#/components/schemas/LocationInfo'
        deliveryMethod:
          type: string
          description: 配送方式
          example: "自提"
          enum: [delivery, pickup, both]
        paymentMethod:
          type: string
          description: 支付方式
          example: "微信支付"
          enum: [wechat, alipay, cash]

    PublishGroupBuyResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                activityId:
                  type: string
                  description: 拼单活动ID
                  example: "activity_789012"
            status:
              type: string
              description: 拼单活动状态
              example: "active"
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"

    JoinGroupBuyRequest:
      type: object
      properties:
        quantity:
          type: integer
          description: 参与数量
          example: 2
          minimum: 1
        remark:
          type: string
          description: 参与备注
          example: "希望能拼单成功"
          maxLength: 200

    JoinGroupBuyResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                participantId:
                  type: string
                  description: 参与者ID
                  example: "participant_789012"
                joinTime:
                  type: string
                  format: date-time
                  description: 参与时间
                  example: "2024-01-15T10:30:00Z"
                currentParticipants:
                  type: integer
                  description: 当前参与人数
                  example: 26
                progress:
                  type: number
                  description: 拼单进度百分比
                  example: 52.0

    UserProfileResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserProfile'

    UserProfile:
      type: object
      properties:
        id:
          type: string
          description: 用户ID
          example: "user_123456"
        phone:
          type: string
          description: 手机号
          example: "138****5678"
        nickname:
          type: string
          description: 用户昵称
          example: "邻里小王"
        avatar:
          type: string
          description: 用户头像
          example: "https://images.example.com/avatar1.jpg"
        gender:
          type: string
          description: 性别
          enum: [male, female, unknown]
          example: "male"
        birthday:
          type: string
          format: date
          description: 生日
          example: "1990-01-15"
        location:
          $ref: '#/components/schemas/LocationInfo'
        community:
          type: object
          properties:
            id:
              type: string
              example: "community_789"
            name:
              type: string
              example: "春题小区"
        verified:
          type: boolean
          description: 是否实名认证
          example: true
        joinTime:
          type: string
          format: date-time
          description: 注册时间
          example: "2024-01-01T10:30:00Z"
        stats:
          type: object
          properties:
            postsCount:
              type: integer
              description: 发布数量
              example: 15
            favoritesCount:
              type: integer
              description: 收藏数量
              example: 32
            creditsScore:
              type: integer
              description: 信用积分
              example: 850

    UpdateProfileRequest:
      type: object
      properties:
        nickname:
          type: string
          description: 用户昵称
          example: "邻里小王"
          maxLength: 20
        avatar:
          type: string
          description: 用户头像URL
          example: "https://images.example.com/avatar1.jpg"
        gender:
          type: string
          description: 性别
          enum: [male, female, unknown]
          example: "male"
        birthday:
          type: string
          format: date
          description: 生日
          example: "1990-01-15"
        location:
          $ref: '#/components/schemas/LocationInfo'
    FavoriteListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                type:
                  type: string
                  enum: [second-hand, housing, parking, market, group-buy]
                dateRange:
                  type: object
                  properties:
                    startDate:
                      type: string
                      format: date
                    endDate:
                      type: string
                      format: date
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, favoriteTime]
                  default: favoriteTime
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    FavoriteListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 32
                totalPages:
                  type: integer
                  example: 2
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/FavoriteItem'

    FavoriteItem:
      type: object
      properties:
        id:
          type: string
          description: 收藏ID
          example: "favorite_123456"
        type:
          type: string
          description: 收藏类型
          enum: [second-hand, housing, parking, market, group-buy]
          example: "second-hand"
        itemId:
          type: string
          description: 收藏项目ID
          example: "product_789012"
        title:
          type: string
          description: 收藏项目标题
          example: "iPhone 14 Pro Max 256GB 深空黑"
        image:
          type: string
          description: 收藏项目图片
          example: "https://images.example.com/phone1.jpg"
        price:
          type: number
          description: 价格
          example: 7999
        status:
          type: string
          description: 项目状态
          example: "active"
        favoriteTime:
          type: string
          format: date-time
          description: 收藏时间
          example: "2024-01-15T10:30:00Z"
    MyPostsListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                type:
                  type: string
                  enum: [second-hand, housing, parking, market, group-buy]
                status:
                  type: string
                  enum: [active, inactive, sold, expired]
                dateRange:
                  type: object
                  properties:
                    startDate:
                      type: string
                      format: date
                    endDate:
                      type: string
                      format: date
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createdAt, updatedAt, viewCount]
                  default: createdAt
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    MyPostsListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 15
                totalPages:
                  type: integer
                  example: 1
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/MyPostItem'

    MyPostItem:
      type: object
      properties:
        id:
          type: string
          description: 发布项目ID
          example: "post_123456"
        type:
          type: string
          description: 发布类型
          enum: [second-hand, housing, parking, market, group-buy]
          example: "second-hand"
        title:
          type: string
          description: 发布项目标题
          example: "iPhone 14 Pro Max 256GB 深空黑"
        image:
          type: string
          description: 发布项目图片
          example: "https://images.example.com/phone1.jpg"
        price:
          type: number
          description: 价格
          example: 7999
        status:
          type: string
          description: 项目状态
          enum: [active, inactive, sold, expired]
          example: "active"
        viewCount:
          type: integer
          description: 浏览次数
          example: 156
        favoriteCount:
          type: integer
          description: 收藏次数
          example: 23
        createTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        updateTime:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-16T14:20:00Z"

    ConversationListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                type:
                  type: string
                  enum: [private, group, system]
                hasUnread:
                  type: boolean
                keyword:
                  type: string
                  maxLength: 100
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [updateTime, createTime]
                  default: updateTime
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    ConversationsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 25
                totalPages:
                  type: integer
                  example: 2
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/ConversationItem'

    ConversationItem:
      type: object
      properties:
        id:
          type: string
          description: 会话ID
          example: "conversation_123456"
        type:
          type: string
          description: 会话类型
          enum: [private, group, system]
          example: "private"
        title:
          type: string
          description: 会话标题
          example: "与张三的对话"
        avatar:
          type: string
          description: 会话头像
          example: "https://images.example.com/avatar1.jpg"
        lastMessage:
          type: object
          properties:
            content:
              type: string
              description: 最后一条消息内容
              example: "你好，请问这个商品还在吗？"
            sendTime:
              type: string
              format: date-time
              description: 发送时间
              example: "2024-01-15T14:30:00Z"
            senderName:
              type: string
              description: 发送者姓名
              example: "张三"
        unreadCount:
          type: integer
          description: 未读消息数量
          example: 3
        updateTime:
          type: string
          format: date-time
          description: 最后更新时间
          example: "2024-01-15T14:30:00Z"
    MessageListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 50
            filters:
              type: object
              properties:
                messageType:
                  type: string
                  enum: [text, image, voice, video, file, system]
                before:
                  type: string
                  description: 获取指定消息ID之前的消息
                after:
                  type: string
                  description: 获取指定消息ID之后的消息
                keyword:
                  type: string
                  maxLength: 100
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [sendTime]
                  default: sendTime
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    MessagesResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 156
                totalPages:
                  type: integer
                  example: 8
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/MessageItem'

    MessageItem:
      type: object
      properties:
        id:
          type: string
          description: 消息ID
          example: "message_123456"
        conversationId:
          type: string
          description: 会话ID
          example: "conversation_789012"
        senderId:
          type: string
          description: 发送者ID
          example: "user_456789"
        senderName:
          type: string
          description: 发送者姓名
          example: "张三"
        senderAvatar:
          type: string
          description: 发送者头像
          example: "https://images.example.com/avatar1.jpg"
        type:
          type: string
          description: 消息类型
          enum: [text, image, voice, video, file, system]
          example: "text"
        content:
          type: string
          description: 消息内容
          example: "你好，请问这个商品还在吗？"
        attachments:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [image, voice, video, file]
              url:
                type: string
              size:
                type: integer
              duration:
                type: integer
          description: 消息附件
        sendTime:
          type: string
          format: date-time
          description: 发送时间
          example: "2024-01-15T14:30:00Z"
        status:
          type: string
          description: 消息状态
          enum: [sending, sent, delivered, read, failed]
          example: "read"
        replyTo:
          type: string
          description: 回复的消息ID
          example: "message_456789"
    NotificationListRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                type:
                  type: string
                  enum: [system, activity, transaction, social]
                status:
                  type: string
                  enum: [unread, read]
                dateRange:
                  type: object
                  properties:
                    startDate:
                      type: string
                      format: date
                    endDate:
                      type: string
                      format: date
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [createTime, type]
                  default: createTime
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    NotificationsResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 45
                totalPages:
                  type: integer
                  example: 3
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/NotificationItem'

    NotificationItem:
      type: object
      properties:
        id:
          type: string
          description: 通知ID
          example: "notification_123456"
        type:
          type: string
          description: 通知类型
          enum: [system, activity, transaction, social]
          example: "transaction"
        title:
          type: string
          description: 通知标题
          example: "订单状态更新"
        content:
          type: string
          description: 通知内容
          example: "您的订单已发货，请注意查收"
        icon:
          type: string
          description: 通知图标
          example: "fas fa-shipping-fast"
        status:
          type: string
          description: 通知状态
          enum: [unread, read]
          example: "unread"
        relatedId:
          type: string
          description: 关联对象ID
          example: "order_789012"
        relatedType:
          type: string
          description: 关联对象类型
          example: "order"
        createTime:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-15T14:30:00Z"
        readTime:
          type: string
          format: date-time
          description: 阅读时间
          example: "2024-01-15T15:00:00Z"

    HomeDashboardResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/HomeDashboard'

    HomeDashboard:
      type: object
      properties:
        user:
          type: object
          properties:
            nickname:
              type: string
              example: "邻里小王"
            avatar:
              type: string
              example: "https://images.example.com/avatar1.jpg"
            community:
              type: string
              example: "春题小区"
            creditsScore:
              type: integer
              example: 850
        stats:
          type: object
          properties:
            myPostsCount:
              type: integer
              description: 我的发布数量
              example: 15
            myFavoritesCount:
              type: integer
              description: 我的收藏数量
              example: 32
            unreadMessagesCount:
              type: integer
              description: 未读消息数量
              example: 5
            unreadNotificationsCount:
              type: integer
              description: 未读通知数量
              example: 3
        recentActivities:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [second-hand, housing, parking, market, group-buy]
              title:
                type: string
              image:
                type: string
              price:
                type: number
              publishTime:
                type: string
                format: date-time
          description: 最近活动
          example:
            - type: "second-hand"
              title: "iPhone 14 Pro Max"
              image: "https://images.example.com/phone1.jpg"
              price: 7999
              publishTime: "2024-01-15T10:30:00Z"
        recommendations:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [second-hand, housing, parking, market, group-buy]
              title:
                type: string
              image:
                type: string
              price:
                type: number
              distance:
                type: number
          description: 推荐内容
          maxItems: 10
    GlobalSearchRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            pageNo:
              type: integer
              minimum: 1
              default: 1
            pageSize:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  maxLength: 100
                modules:
                  type: array
                  items:
                    type: string
                    enum: [second-hand, housing, parking, market, group-buy]
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                    max:
                      type: number
                location:
                  $ref: '#/components/schemas/LocationInfo'
            sorting:
              type: object
              properties:
                field:
                  type: string
                  enum: [relevance, createdAt, price]
                  default: relevance
                order:
                  type: string
                  enum: [asc, desc]
                  default: desc

    GlobalSearchResponse:
      allOf:
        - $ref: '#/components/schemas/BaseSuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                total:
                  type: integer
                  example: 89
                totalPages:
                  type: integer
                  example: 5
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/GlobalSearchItem'

    GlobalSearchItem:
      type: object
      properties:
        id:
          type: string
          description: 搜索结果ID
          example: "item_123456"
        type:
          type: string
          description: 搜索结果类型
          enum: [second-hand, housing, parking, market, group-buy]
          example: "second-hand"
        title:
          type: string
          description: 搜索结果标题
          example: "iPhone 14 Pro Max 256GB 深空黑"
        description:
          type: string
          description: 搜索结果描述
          example: "个人自用，9.5成新，无磕碰，功能正常"
        image:
          type: string
          description: 搜索结果图片
          example: "https://images.example.com/phone1.jpg"
        price:
          type: number
          description: 价格
          example: 7999
        location:
          $ref: '#/components/schemas/LocationInfo'
        distance:
          type: number
          description: 距离（公里）
          example: 2.5
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        relevanceScore:
          type: number
          description: 相关性评分
          example: 0.95
        status:
          type: string
          description: 状态
          example: "active"

tags:
  - name: 用户认证
    description: 用户登录、注册、短信验证、密码管理、第三方登录等认证功能
  - name: 社区管理
    description: 小区选择、加入、信息管理等功能
  - name: 首页导航
    description: 首页推荐、全局搜索、发现页面等导航功能
  - name: 二手闲置
    description: 二手商品发布、浏览、交易等功能
  - name: 房屋租赁
    description: 房源发布、搜索、租赁管理等功能
  - name: 停车位
    description: 停车位发布、搜索、租售管理等功能
  - name: 邻里集市
    description: 农产品交易、农户管理、订单处理等功能
  - name: 邻里拼单
    description: 拼单活动管理、参与拼单等功能
  - name: 用户中心
    description: 个人信息、收藏、发布记录等用户管理功能
  - name: 消息通知
    description: 会话管理、消息收发、通知管理等功能
  - name: 公共服务
    description: 文件上传、地理位置服务等公共功能
